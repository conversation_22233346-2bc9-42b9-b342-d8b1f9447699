# Database Configuration
DB_NAME="your_database_name"
DB_USER="your_database_user"
DB_PASSWORD="your_database_password"
DB_HOST="your_database_host"
DB_PORT="5432"

# Azure Storage Configuration
AZURE_STORAGE_ACCOUNT_NAME="your_storage_account_name"
AZURE_STORAGE_ACCOUNT_KEY="your_storage_account_key"
AZURE_STORAGE_CONTAINER_NAME="your_container_name"
AZURE_STORAGE_CONNECTION_STRING="your_storage_connection_string"

# Firebase Configuration
FIREBASE_FUNCTIONS_URL="your_firebase_functions_url"
FIREBASE_FUNCTIONS_KEY="your_firebase_functions_key"
FIREBASE_PROJECT_ID="your_firebase_project_id"
FIREBASE_PRIVATE_KEY_ID="your_firebase_private_key_id"
FIREBASE_PRIVATE_KEY="your_firebase_private_key"
FIREBASE_CLIENT_EMAIL="your_firebase_client_email"
FIREBASE_CLIENT_ID="your_firebase_client_id"
FIREBASE_CLIENT_X509_CERT_URL="your_firebase_client_x509_cert_url"

# JWT Secrets
ACCESS_TOKEN_SECRET="your_access_token_secret"
REFRESH_TOKEN_SECRET="your_refresh_token_secret"

# SMTP Configuration
SMTP_USER="your_smtp_user"
SMTP_PASSWORD="your_smtp_password"

# SendGrid Configuration
SENDGRID_API_KEY="your_sendgrid_api_key"

# Azure B2C Configuration
B2C_CLIENT_ID="your_b2c_client_id"
B2C_CLIENT_SECRET="your_b2c_client_secret"
B2C_TENANT_ID="your_b2c_tenant_id"

# Azure Web PubSub Configuration
AZURE_PUBSUB_CONNECTION_STRING="your_azure_pubsub_connection_string"

# Azure AD Configuration (for authConfig.js)
CLIENT_ID="your_client_id"
CLIENT_SECRET="your_client_secret"
CLOUD_INSTANCE="https://login.microsoftonline.com/"
TENANT_ID="your_tenant_id"
REDIRECT_URI="your_redirect_uri"
POST_LOGOUT_REDIRECT_URI="your_post_logout_redirect_uri"
GRAPH_API_ENDPOINT="https://graph.microsoft.com/"
