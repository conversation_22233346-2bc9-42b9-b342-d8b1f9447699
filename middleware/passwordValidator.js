/**
 * Password validation middleware and utility functions
 * Enforces strong password requirements for security
 */

const passwordRequirements = {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?'
};

/**
 * Validates password strength according to security requirements
 * @param {string} password - The password to validate
 * @returns {object} - Validation result with success status and messages
 */
function validatePassword(password) {
    const errors = [];
    
    if (!password) {
        return {
            isValid: false,
            errors: ['Password is required']
        };
    }

    // Check minimum length
    if (password.length < passwordRequirements.minLength) {
        errors.push(`Password must be at least ${passwordRequirements.minLength} characters long`);
    }

    // Check maximum length
    if (password.length > passwordRequirements.maxLength) {
        errors.push(`Password must not exceed ${passwordRequirements.maxLength} characters`);
    }

    // Check for uppercase letters
    if (passwordRequirements.requireUppercase && !/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }

    // Check for lowercase letters
    if (passwordRequirements.requireLowercase && !/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }

    // Check for numbers
    if (passwordRequirements.requireNumbers && !/\d/.test(password)) {
        errors.push('Password must contain at least one number');
    }

    // Check for special characters
    if (passwordRequirements.requireSpecialChars) {
        const specialCharRegex = new RegExp(`[${passwordRequirements.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`);
        if (!specialCharRegex.test(password)) {
            errors.push(`Password must contain at least one special character (${passwordRequirements.specialChars})`);
        }
    }

    // Check for common weak patterns
    const commonPatterns = [
        /(.)\1{2,}/, // Three or more consecutive identical characters
        /123456|654321|abcdef|qwerty|password|admin/i, // Common sequences
    ];

    for (const pattern of commonPatterns) {
        if (pattern.test(password)) {
            errors.push('Password contains common patterns and is too weak');
            break;
        }
    }

    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

/**
 * Express middleware for password validation
 * @param {string} passwordField - The field name containing the password (default: 'password')
 * @returns {Function} - Express middleware function
 */
function passwordValidationMiddleware(passwordField = 'password') {
    return (req, res, next) => {
        let password;
        
        // Extract password from different possible request structures
        if (req.body.r_data && req.body.r_data[passwordField]) {
            password = req.body.r_data[passwordField];
        } else if (req.body.pr_data && req.body.pr_data[passwordField]) {
            password = req.body.pr_data[passwordField];
        } else if (req.body.data && req.body.data[passwordField]) {
            password = req.body.data[passwordField];
        } else if (req.body[passwordField]) {
            password = req.body[passwordField];
        }

        if (!password) {
            return res.status(400).json({
                successful: false,
                message: 'Password is required',
                errors: ['Password field is missing']
            });
        }

        const validation = validatePassword(password);
        
        if (!validation.isValid) {
            return res.status(400).json({
                successful: false,
                message: 'Password does not meet security requirements',
                errors: validation.errors,
                requirements: {
                    minLength: passwordRequirements.minLength,
                    maxLength: passwordRequirements.maxLength,
                    mustContain: [
                        'At least one uppercase letter',
                        'At least one lowercase letter', 
                        'At least one number',
                        'At least one special character',
                        'No common patterns or sequences'
                    ]
                }
            });
        }

        next();
    };
}

/**
 * Get password requirements for client-side validation
 * @returns {object} - Password requirements object
 */
function getPasswordRequirements() {
    return {
        minLength: passwordRequirements.minLength,
        maxLength: passwordRequirements.maxLength,
        requireUppercase: passwordRequirements.requireUppercase,
        requireLowercase: passwordRequirements.requireLowercase,
        requireNumbers: passwordRequirements.requireNumbers,
        requireSpecialChars: passwordRequirements.requireSpecialChars,
        specialChars: passwordRequirements.specialChars,
        description: [
            `Minimum ${passwordRequirements.minLength} characters`,
            `Maximum ${passwordRequirements.maxLength} characters`,
            'At least one uppercase letter',
            'At least one lowercase letter',
            'At least one number',
            'At least one special character',
            'No common patterns or sequences'
        ]
    };
}

module.exports = {
    validatePassword,
    passwordValidationMiddleware,
    getPasswordRequirements
};
