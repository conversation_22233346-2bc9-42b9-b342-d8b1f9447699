const jwt = require('jsonwebtoken');
const passport = require('passport');
const { accessTokenSecret } = require('../config');

const authenticateJWT = (req, res, next) => {
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1];

        // Validate token exists and is not empty
        if (!token || token.trim() === '') {
            return res.status(401).json({
                successful: false,
                message: 'Invalid token format'
            });
        }

        // Try to decode the token to determine its type
        try {
            const decoded = jwt.decode(token, { complete: true });

            // Check if it's an Azure AD B2C token by examining the issuer
            if (decoded && decoded.payload && decoded.payload.iss &&
                decoded.payload.iss.includes('b2clogin.com')) {

                // Handle Azure AD B2C token
                passport.authenticate('azuread-openidconnect', { session: false }, (err, user, info) => {
                    if (err) {
                        console.error('Azure AD authentication error:', err);
                        return res.status(403).json({
                            successful: false,
                            message: 'Authentication failed'
                        });
                    }

                    if (!user) {
                        return res.status(401).json({
                            successful: false,
                            message: 'Invalid Azure AD token'
                        });
                    }

                    req.user = user;
                    next();
                })(req, res, next);
            } else {
                // Handle regular JWT token with expiration check
                jwt.verify(token, accessTokenSecret, {
                    algorithms: ['HS256']
                }, (err, user) => {
                    if (err) {
                        console.error('JWT verification error:', err.message);
                        if (err.name === 'TokenExpiredError') {
                            return res.status(401).json({
                                successful: false,
                                message: 'Token has expired'
                            });
                        } else if (err.name === 'JsonWebTokenError') {
                            return res.status(401).json({
                                successful: false,
                                message: 'Invalid token'
                            });
                        }
                        return res.status(403).json({
                            successful: false,
                            message: 'Token verification failed'
                        });
                    }

                    req.user = user;
                    next();
                });
            }
        } catch (decodeError) {
            console.error('Token decode error:', decodeError);
            return res.status(401).json({
                successful: false,
                message: 'Invalid token format'
            });
        }
    } else {
        res.status(401).json({
            successful: false,
            message: 'Authorization header missing or invalid format'
        });
    }
};

module.exports = {
    authenticateJWT,
};
