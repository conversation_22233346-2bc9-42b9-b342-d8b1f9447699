const jwt = require('jsonwebtoken');
const passport = require('passport');
const { accessTokenSecret } = require('../config');

const authenticateJWT = (req, res, next) => {
    const authHeader = req.headers.authorization;

    if (authHeader) {
        const token = authHeader.split(' ')[1];

        // Check for Azure AD B2C token
        if (token.startsWith('Bearer ')) {
            passport.authenticate('azuread-openidconnect', { session: false }, (err, user, info) => {
                if (err) {
                    return res.sendStatus(403);
                }

                if (!user) {
                    return res.sendStatus(401);
                }

                req.user = user;
                next();
            })(req, res, next);
        } else {
            // Check for your existing JWT token
            jwt.verify(token, accessTokenSecret, (err, user) => {
                if (err) {
                    return res.sendStatus(403);
                }

                req.user = user;
                next();
            });
        }
    } else {
        res.sendStatus(401);
    }
};

module.exports = {
    authenticateJWT,
};
