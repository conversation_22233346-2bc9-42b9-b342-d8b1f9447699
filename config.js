const nodemailer = require('nodemailer');
require('dotenv').config();

const saltRounds = 10;
const accessTokenSecret = process.env.ACCESS_TOKEN_SECRET;
const refreshTokenSecret = process.env.REFRESH_TOKEN_SECRET;

// Validate required environment variables
if (!accessTokenSecret || !refreshTokenSecret) {
    console.error('CRITICAL: JWT secrets not found in environment variables');
    console.error('Please set ACCESS_TOKEN_SECRET and REFRESH_TOKEN_SECRET in your .env file');
    process.exit(1);
}

let smtpTransport = nodemailer.createTransport({
    service: "gmail",
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
    },
    tls:{
        rejectUnauthorized: false
    }
});

// Validate SMTP configuration
if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
    console.error('WARNING: SMTP credentials not found in environment variables');
    console.error('Email functionality will not work. Please set SMTP_USER and SMTP_PASSWORD in your .env file');
}

module.exports={
    saltRounds:saltRounds,
    accessTokenSecret:accessTokenSecret,
    refreshTokenSecret:refreshTokenSecret,
    smtpTransport:smtpTransport
}