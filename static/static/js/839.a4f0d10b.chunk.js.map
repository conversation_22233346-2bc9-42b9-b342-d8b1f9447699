{"version": 3, "file": "static/js/839.a4f0d10b.chunk.js", "mappings": ";0RAKa,MAAAA,EACgB,4BADhBA,EAEG,cAFHA,EAGE,aAHFA,EAIO,kBAJPA,EAKQ,mBALRA,EAMC,WANDA,EAOW,sBAPXA,EAQiB,oCCsBjBC,EAMTC,WAAAA,CACIC,EACAC,EACAC,EACAC,GAEAC,KAAKJ,SAAWA,EAChBI,KAAKH,mBAAqBA,EAC1BG,KAAKF,OAASA,EACdE,KAAKD,OAASA,EAGXE,iBAAAA,CACHC,GAIsB,IAAAC,EAEtB,IAAIC,EAEAA,OADiCC,IAAjCH,EAAQI,qBACM,IAAIC,IAEJ,IAAIA,IACdC,OAAOC,QAAQP,EAAQI,uBAI/B,MACMI,GADiB,IAAIC,EAAAA,GACGC,8BAC1BV,EAAQQ,OACRV,KAAKH,oBAEHgB,EAASX,EAAQW,QAAUC,EAAAA,GAiBjC,MAhBmC,CAC/BC,iBAAiC,QAAjBZ,EAAED,EAAQc,eAAO,IAAAb,OAAA,EAAfA,EAAiBc,cACnCrB,SAAUI,KAAKJ,SACfsB,UAAWhB,EAAQgB,UACnBC,MAAON,EAAOO,KAAK,KACnBC,mBAC8BhB,IAA1BH,EAAQmB,cACFnB,EAAQmB,cACRrB,KAAKF,OAAOwB,gBACtBZ,OAASa,EAAAA,EAAYC,WAAWd,QAAmBL,EAATK,EAC1Ce,MAAOvB,EAAQuB,MACfC,qBACIxB,EAAQwB,sBAAwBC,EAAAA,GAAqBC,OACzDC,gBAAiBzB,GAMlB0B,oBAAAA,CACH5B,EACA6B,EACAC,GAEA,IAAKD,EAASE,MAAMC,WAAaH,EAASE,MAAME,aAC5C,MAAMC,EAAAA,EAAAA,IAAsBC,EAAAA,IAGhC,MAAMC,EAAY,IAAIC,KACkC,KAAnDP,GAAgBD,EAASE,MAAMO,YAAc,KAE5CC,EAAgBC,EAAAA,GAClBX,EAASE,MAAMC,SACflC,KAAKF,OAAO6C,cAEV3B,EAAUhB,KAAK4C,mBACjBb,EAASf,QACTyB,GAEE5B,EAASkB,EAASE,MAAMd,OAASjB,EAAQiB,MAoB/C,MAlBmD,CAC/CD,UAAWa,EAASE,MAAMf,WAAaF,EAAQ6B,YAC/CC,SAAU9B,EAAQ+B,eAClBC,SAAUhC,EAAQgC,SAClBnC,OAAQA,EAAOoC,MAAM,KACrBjC,UACAkC,QAASnB,EAASE,MAAMC,SACxBO,gBACAU,YAAapB,EAASE,MAAME,aAC5BiB,WAAW,EACXd,UAAWA,EACXe,UACInD,EAAQwB,sBAAwBC,EAAAA,GAAqBC,OACzDP,cAAenB,EAAQmB,cACvBiC,aAAchB,EACdb,MAAOvB,EAAQuB,OA4BhBmB,kBAAAA,CACHW,EACAd,GAEA,MAAMe,EACFf,GAAkBc,EAAYd,cAE5BM,EACFQ,EAAYR,iBACU,OAAtBS,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBC,OACF,OAAtBD,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBE,MACxB,GAEEV,EACFO,EAAYP,WAAkC,OAAtBQ,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBG,MAAO,GAErD1C,EACFsC,EAAYtC,eAAiB,GAAJ2C,OAAOb,EAAc,KAAAa,OAAIZ,GAEhDa,EACFN,EAAYM,WACU,OAAtBL,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBM,qBACxB,GAEEC,EAAOR,EAAYQ,OAA8B,OAAtBP,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBO,MAazD,MAXiC,CAC7B9C,gBACA4B,YAAaU,EAAYV,YACzBG,WACAa,WACAd,iBACAgB,OACAb,QAASK,EAAYL,QACrBT,cAAee,GAWhBQ,eAAAA,CACHC,GAOA,ICrMF,SAAwBA,GAC1B,YAAyC5D,IAAjC4D,EAAsBC,MAClC,CDmMYC,CAAcF,GAoCd,OAAO,IAAIG,EAAAA,GAAU,gBAAiB,6BAnCtC,OAAQH,EAAMC,QACV,KAAKzE,EACD,OAAO,IAAI4E,EAAAA,GACPhC,EAAAA,IAER,KAAK5C,EACD,OAAO,IAAI4E,EAAAA,GACPhC,EAAAA,IAER,KAAK5C,EACD,OAAO,IAAI4E,EAAAA,GACPhC,EAAAA,IAER,KAAK5C,EACD,OAAO,IAAI4E,EAAAA,GACPhC,EAAAA,IAER,KAAK5C,EACD,OAAO,IAAI4E,EAAAA,GACPJ,EAAMK,MACFjC,EAAAA,GACJ4B,EAAMM,aAEd,KAAK9E,EACL,KAAKA,EACD,OAAO,IAAI+E,EAAAA,EAAYP,EAAMK,KAAML,EAAMM,aAC7C,KAAK9E,EACD,OAAO,IAAIgF,EAAAA,GACPR,EAAMK,KACNL,EAAMM,aAEd,QACI,OAAO,IAAIH,EAAAA,GAAUH,EAAMK,KAAML,EAAMM,eE3O9C,MAAAG,EACU,CACfJ,KAAM,qBACNK,KAAM,gEAIR,MAAOC,UAA2BR,EAAAA,GACpCzE,WAAAA,CAAYkF,EAAmBC,GAC3BC,MAAMF,EAAWC,GAEjBtE,OAAOwE,eAAehF,KAAM4E,EAAmBK,WAC/CjF,KAAK+D,KAAO,qBAGT,6BAAOmB,GACV,OAAO,IAAIN,EACPF,EAA4CJ,KAC5CI,EAA4CC,qCCmB3CQ,EAyBTxF,WAAAA,CAAYyF,GACRpF,KAAKoF,iBAAmBA,EACxB,MAAMC,EAAQrF,KAAKoF,iBAAiBE,iBACpC,QAAcjF,IAAVgF,EAGA,MAAM,IAAIE,MAAM,wCAFhBvF,KAAKwF,YAAcH,EAMvBrF,KAAKyF,OAASL,EAAiBM,YAG/B1F,KAAKD,OAASC,KAAKoF,iBAAiBO,YAEpC3F,KAAK4F,kBAAoB5F,KAAKyF,OAAOI,UAAUC,OAG/C9F,KAAK+F,cAAgBX,EAAiBY,uBAChC,IAAIC,EAAAA,EAAUjG,KAAKD,OAAQC,KAAK4F,mBAChCM,EAAAA,EAENlG,KAAKmG,aAAe,IAAIC,EAAAA,EAAapG,KAAKD,OAAQC,KAAK+F,eAEvD/F,KAAKqG,qBAAuB,IAAI3G,EAC5BM,KAAKyF,OAAOa,KAAK1G,SACjBI,KAAKyF,OAAOa,KAAKzG,mBACjBG,KAAK+F,cACL/F,KAAKD,QAGbwG,iBAAAA,GACI,MAAM3B,EAAmBM,yBAG7BsB,eAAAA,GACI,OAAOxG,KAAKmG,aAGhB,6BAAaM,CACTrB,GAEA,MAAMsB,EAAa,IAAIvB,EAAwBC,GAC/C,OAAOuB,QAAQC,QAAQF,GAG3BG,UAAAA,GAEI,OAAOF,QAAQC,UAGXE,kBAAAA,CAMN5G,GACE,OAAW,OAAPA,QAAO,IAAPA,GAAAA,EAASmB,cACFnB,EAEJ,IACAA,EACHmB,cAAerB,KAAK+F,cAAczE,iBAIlC,6BAAMyF,CACV7G,GAEA,MAAM8G,EAAehH,KAAK8G,mBAAmB5G,GAE7CF,KAAKmG,aAAac,UACdC,EAAAA,EAAUC,oBACVC,EAAAA,GAAgBC,MAChBL,GAGJ,MAAMM,EAAqBtH,KAAK4F,kBAAkB2B,iBAC9CC,EAAAA,GAAkBC,kBAClBT,EAAa3F,eAGC,OAAlBiG,QAAkB,IAAlBA,GAAAA,EAAoBI,IAAI,CAAEC,sBAAsB,IAEhD,IACI,MAAMC,EACF5H,KAAKqG,qBAAqBpG,kBAAkB+G,GAC1ChF,EAAe6F,EAAAA,KACf9F,QAAiB/B,KAAKwF,YAAYsC,oBACpCF,GAEEG,EACF/H,KAAKqG,qBAAqBvE,qBACtB8F,EACA7F,EACAC,GAoBR,OAjBAhC,KAAKoF,iBAAiB4C,iBAAiBD,EAAO/G,SAC9ChB,KAAKmG,aAAac,UACdC,EAAAA,EAAUe,sBACVb,EAAAA,GAAgBC,MAChBU,GAGJT,EAAmBI,IAAI,CACnBQ,gBAAiBH,EAAO5E,YAAYgF,OACpCC,YAAaL,EAAO7E,QAAQiF,SAGhCb,EAAmBe,IAAI,CACnBC,SAAS,EACTC,UAAWR,EAAOQ,YAGfR,CACV,CAAC,MAAOS,GACL,MAAMvE,EAAQjE,KAAKqG,qBAAqBrC,gBAAgBwE,GAexD,MAdAxI,KAAKmG,aAAac,UACdC,EAAAA,EAAUuB,sBACVrB,EAAAA,GAAgBC,MAChB,KACAmB,GAGJlB,EAAmBe,IACf,CACIC,SAAS,GAEbE,GAGEvE,CACT,EAGG,gCAAMyE,CACVxI,GAEA,MAAM8G,EAAehH,KAAK8G,mBAAmB5G,GAC7CF,KAAKmG,aAAac,UACdC,EAAAA,EAAUC,oBACVC,EAAAA,GAAgBuB,OAChB3B,GAGJ,MAAM4B,EAAuB5I,KAAK4F,kBAAkB2B,iBAChDC,EAAAA,GAAkBqB,UAClB7B,EAAa3F,eAGG,OAApBuH,QAAoB,IAApBA,GAAAA,EAAsBE,UAAU,CAC5BC,sBAAuB,IAGP,OAApBH,QAAoB,IAApBA,GAAAA,EAAsBlB,IAAI,CACtBC,sBAAsB,IAG1B,IACI,MAAMC,EACF5H,KAAKqG,qBAAqBpG,kBAAkB+G,GAC1ChF,EAAe6F,EAAAA,KACf9F,QAAiB/B,KAAKwF,YAAYwD,eAAepB,GAEjDG,EACF/H,KAAKqG,qBAAqBvE,qBACtB8F,EACA7F,EACAC,GAiBR,OAdAhC,KAAKoF,iBAAiB4C,iBAAiBD,EAAO/G,SAC9ChB,KAAKmG,aAAac,UACdC,EAAAA,EAAUe,sBACVb,EAAAA,GAAgBuB,OAChBZ,GAEgB,OAApBa,QAAoB,IAApBA,GAAAA,EAAsBlB,IAAI,CACtBQ,gBAAiBH,EAAO5E,YAAYgF,OACpCC,YAAaL,EAAO7E,QAAQiF,SAEZ,OAApBS,QAAoB,IAApBA,GAAAA,EAAsBP,IAAI,CACtBC,SAAS,EACTC,UAAWR,EAAOQ,YAEfR,CACV,CAAC,MAAOS,GACL,MAAMvE,EAAQjE,KAAKqG,qBAAqBrC,gBAAgBwE,GAaxD,MAZAxI,KAAKmG,aAAac,UACdC,EAAAA,EAAUuB,sBACVrB,EAAAA,GAAgBuB,OAChB,KACAH,GAEgB,OAApBI,QAAoB,IAApBA,GAAAA,EAAsBP,IAClB,CACIC,SAAS,GAEbE,GAEEvE,CACT,EAGL,uBAAMgF,CACF/I,GAEA,OAAOF,KAAK+G,wBAAwB7G,GAGxCgJ,oBAAAA,CAAqBhJ,GACjB,MAAM0E,EAAmBM,yBAG7B,wBAAMiE,CACFC,GAEA,OAAOpJ,KAAK0I,2BAA2BU,GAI3CC,kBAAAA,CACInJ,GAEA,MAAM0E,EAAmBM,yBAE7BoE,kBAAAA,CACIpJ,EAaAqJ,EACAC,GAEA,MAAM5E,EAAmBM,yBAE7BuE,0BAAAA,CACIC,EACAN,GAEA,MAAMxE,EAAmBM,yBAO7ByE,gBAAAA,CAAiBC,GACb,OAAO5J,KAAKmG,aAAawD,iBAAiBC,GAO9CC,mBAAAA,CAAoBC,GAChB9J,KAAKmG,aAAa0D,oBAAoBC,GAI1CC,sBAAAA,CAAuBH,GACnB,MAAMhF,EAAmBM,yBAG7B8E,yBAAAA,CAA0BF,GACtB,MAAMlF,EAAmBM,yBAE7B+E,0BAAAA,GACI,MAAMrF,EAAmBM,yBAE7BgF,2BAAAA,GACI,MAAMtF,EAAmBM,yBAI7BiF,UAAAA,CAAWC,GACP,MAAMxF,EAAmBM,yBAI7BmF,kBAAAA,CAAmBpJ,GACf,MAAMqJ,EAAiBtK,KAAKoF,iBAAiBmF,mBAC7C,YAAuBlK,IAAnBiK,GACIA,EAAerJ,gBAAkBA,EAC1BjB,KAAKqG,qBAAqBzD,mBAC7B0H,GAMD,KAIfE,mBAAAA,CAAoBC,GAChB,MAAMH,EAAiBtK,KAAKoF,iBAAiBmF,mBAC7C,YAAuBlK,IAAnBiK,GACIA,EAAevH,iBAAmB0H,EAC3BzK,KAAKqG,qBAAqBzD,mBAC7B0H,GAMD,KAIfI,oBAAAA,CAAqBC,GACjB,MAAML,EAAiBtK,KAAKoF,iBAAiBmF,mBAC7C,YAAuBlK,IAAnBiK,GACIA,EAAezG,WAAa8G,EACrB3K,KAAKqG,qBAAqBzD,mBAC7B0H,GAMD,KAGfM,cAAAA,GACI,MAAMN,EAAiBtK,KAAKoF,iBAAiBmF,mBAC7C,YAAuBlK,IAAnBiK,EACO,CACHtK,KAAKqG,qBAAqBzD,mBAAmB0H,IAG1C,GAGfO,qBAAAA,CACIC,GAEA,OAAOnE,QAAQC,QAAQ,MAE3BmE,UAAAA,CACI7K,GAEA,OAAOF,KAAK+G,wBAAwB7G,GAAW8K,EAAAA,IAGnDC,aAAAA,CAAc/K,GACV,MAAM0E,EAAmBM,yBAG7BgG,MAAAA,CAAOC,GACH,MAAMvG,EAAmBM,yBAE7BkG,cAAAA,CACID,GAEA,MAAMvG,EAAmBM,yBAE7BmG,WAAAA,CACIF,GAEA,MAAMvG,EAAmBM,yBAE7BoG,SAAAA,CAEIpL,GAWA,OAAOF,KAAK0I,2BAA2BxI,GAE3CqL,aAAAA,GACI,MAAM3G,EAAmBM,yBAMtBS,SAAAA,GACH,OAAO3F,KAAKD,OAOhByL,SAAAA,CAAUzL,GACNC,KAAKD,OAASA,EAIlBiI,gBAAAA,CAAiBhH,GAKbhB,KAAKD,OAAO0L,QAAQ,mDAGxBlB,gBAAAA,GACI,MAAMD,EAAiBtK,KAAKoF,iBAAiBmF,mBAC7C,YAAuBlK,IAAnBiK,EACOtK,KAAKqG,qBAAqBzD,mBAAmB0H,GAE7C,KAIfoB,wBAAAA,CAAyBC,EAAiBC,IAQ1CC,mBAAAA,CAAoBC,GAChB9L,KAAKD,OAAO0L,QACR,2DAGRM,gBAAAA,GACI,OAAO/L,KAAKyF,OAEhBuG,YAAAA,GACI,OAAOhM,KAAKoF,iBAAiBY,uBAEjCiG,gBAAAA,GACI,OAAOjM,KAAK+F,cAEhBmG,oBAAAA,GACI,MAAMtH,EAAmBM,yBAG7BiH,mBAAAA,GACI,MAAMvH,EAAmBM,yBAI7B,gBAAMkH,CAAWjB,GACb,MAAMvG,EAAmBM,yBAI7B,kBAAMmH,CAEFtE,EAEA7H,GAMA,MAAM0E,EAAmBM", "sources": ["../node_modules/@azure/msal-browser/src/naa/BridgeStatusCode.ts", "../node_modules/@azure/msal-browser/src/naa/mapping/NestedAppAuthAdapter.ts", "../node_modules/@azure/msal-browser/src/naa/BridgeError.ts", "../node_modules/@azure/msal-browser/src/error/NestedAppAuthError.ts", "../node_modules/@azure/msal-browser/src/controllers/NestedAppAuthController.ts"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nexport const BridgeStatusCode = {\n    UserInteractionRequired: \"USER_INTERACTION_REQUIRED\",\n    UserCancel: \"USER_CANCEL\",\n    NoNetwork: \"NO_NETWORK\",\n    TransientError: \"TRANSIENT_ERROR\",\n    PersistentError: \"PERSISTENT_ERROR\",\n    Disabled: \"DISABLED\",\n    AccountUnavailable: \"ACCOUNT_UNAVAILABLE\",\n    NestedAppAuthUnavailable: \"NESTED_APP_AUTH_UNAVAILABLE\", // NAA is unavailable in the current context, can retry with standard browser based auth\n} as const;\nexport type BridgeStatusCode =\n    (typeof BridgeStatusCode)[keyof typeof BridgeStatusCode];\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { TokenRequest } from \"../TokenRequest\";\nimport { AccountInfo as NaaAccountInfo } from \"../AccountInfo\";\nimport { RedirectRequest } from \"../../request/RedirectRequest\";\nimport { PopupRequest } from \"../../request/PopupRequest\";\nimport {\n    AccountInfo as MsalAccountInfo,\n    AuthError,\n    ClientAuthError,\n    ClientConfigurationError,\n    InteractionRequiredAuthError,\n    ServerError,\n    ICrypto,\n    Logger,\n    AuthToken,\n    TokenClaims,\n    ClientAuthErrorCodes,\n    AuthenticationScheme,\n    RequestParameterBuilder,\n    StringUtils,\n    createClientAuthError,\n    OIDC_DEFAULT_SCOPES,\n} from \"@azure/msal-common\";\nimport { isBridgeError } from \"../BridgeError\";\nimport { BridgeStatusCode } from \"../BridgeStatusCode\";\nimport { AuthenticationResult } from \"../../response/AuthenticationResult\";\nimport {} from \"../../error/BrowserAuthErrorCodes\";\nimport { AuthResult } from \"../AuthResult\";\nimport { SsoSilentRequest } from \"../../request/SsoSilentRequest\";\nimport { SilentRequest } from \"../../request/SilentRequest\";\n\nexport class NestedAppAuthAdapter {\n    protected crypto: ICrypto;\n    protected logger: Logger;\n    protected clientId: string;\n    protected clientCapabilities: string[];\n\n    constructor(\n        clientId: string,\n        clientCapabilities: string[],\n        crypto: ICrypto,\n        logger: Logger\n    ) {\n        this.clientId = clientId;\n        this.clientCapabilities = clientCapabilities;\n        this.crypto = crypto;\n        this.logger = logger;\n    }\n\n    public toNaaTokenRequest(\n        request:\n            | PopupRequest\n            | RedirectRequest\n            | SilentRequest\n            | SsoSilentRequest\n    ): TokenRequest {\n        let extraParams: Map<string, string>;\n        if (request.extraQueryParameters === undefined) {\n            extraParams = new Map<string, string>();\n        } else {\n            extraParams = new Map<string, string>(\n                Object.entries(request.extraQueryParameters)\n            );\n        }\n\n        const requestBuilder = new RequestParameterBuilder();\n        const claims = requestBuilder.addClientCapabilitiesToClaims(\n            request.claims,\n            this.clientCapabilities\n        );\n        const scopes = request.scopes || OIDC_DEFAULT_SCOPES;\n        const tokenRequest: TokenRequest = {\n            platformBrokerId: request.account?.homeAccountId,\n            clientId: this.clientId,\n            authority: request.authority,\n            scope: scopes.join(\" \"),\n            correlationId:\n                request.correlationId !== undefined\n                    ? request.correlationId\n                    : this.crypto.createNewGuid(),\n            claims: !StringUtils.isEmptyObj(claims) ? claims : undefined,\n            state: request.state,\n            authenticationScheme:\n                request.authenticationScheme || AuthenticationScheme.BEARER,\n            extraParameters: extraParams,\n        };\n\n        return tokenRequest;\n    }\n\n    public fromNaaTokenResponse(\n        request: TokenRequest,\n        response: AuthResult,\n        reqTimestamp: number\n    ): AuthenticationResult {\n        if (!response.token.id_token || !response.token.access_token) {\n            throw createClientAuthError(ClientAuthErrorCodes.nullOrEmptyToken);\n        }\n\n        const expiresOn = new Date(\n            (reqTimestamp + (response.token.expires_in || 0)) * 1000\n        );\n        const idTokenClaims = AuthToken.extractTokenClaims(\n            response.token.id_token,\n            this.crypto.base64Decode\n        );\n        const account = this.fromNaaAccountInfo(\n            response.account,\n            idTokenClaims\n        );\n        const scopes = response.token.scope || request.scope;\n\n        const authenticationResult: AuthenticationResult = {\n            authority: response.token.authority || account.environment,\n            uniqueId: account.localAccountId,\n            tenantId: account.tenantId,\n            scopes: scopes.split(\" \"),\n            account,\n            idToken: response.token.id_token,\n            idTokenClaims,\n            accessToken: response.token.access_token,\n            fromCache: true,\n            expiresOn: expiresOn,\n            tokenType:\n                request.authenticationScheme || AuthenticationScheme.BEARER,\n            correlationId: request.correlationId,\n            extExpiresOn: expiresOn,\n            state: request.state,\n        };\n\n        return authenticationResult;\n    }\n\n    /*\n     *  export type AccountInfo = {\n     *     homeAccountId: string;\n     *     environment: string;\n     *     tenantId: string;\n     *     username: string;\n     *     localAccountId: string;\n     *     name?: string;\n     *     idToken?: string;\n     *     idTokenClaims?: TokenClaims & {\n     *         [key: string]:\n     *             | string\n     *             | number\n     *             | string[]\n     *             | object\n     *             | undefined\n     *             | unknown;\n     *     };\n     *     nativeAccountId?: string;\n     *     authorityType?: string;\n     * };\n     */\n    public fromNaaAccountInfo(\n        fromAccount: NaaAccountInfo,\n        idTokenClaims?: TokenClaims\n    ): MsalAccountInfo {\n        const effectiveIdTokenClaims =\n            idTokenClaims || (fromAccount.idTokenClaims as TokenClaims);\n\n        const localAccountId =\n            fromAccount.localAccountId ||\n            effectiveIdTokenClaims?.oid ||\n            effectiveIdTokenClaims?.sub ||\n            \"\";\n\n        const tenantId =\n            fromAccount.tenantId || effectiveIdTokenClaims?.tid || \"\";\n\n        const homeAccountId =\n            fromAccount.homeAccountId || `${localAccountId}.${tenantId}`;\n\n        const username =\n            fromAccount.username ||\n            effectiveIdTokenClaims?.preferred_username ||\n            \"\";\n\n        const name = fromAccount.name || effectiveIdTokenClaims?.name;\n\n        const account: MsalAccountInfo = {\n            homeAccountId,\n            environment: fromAccount.environment,\n            tenantId,\n            username,\n            localAccountId,\n            name,\n            idToken: fromAccount.idToken,\n            idTokenClaims: effectiveIdTokenClaims,\n        };\n\n        return account;\n    }\n\n    /**\n     *\n     * @param error BridgeError\n     * @returns AuthError, ClientAuthError, ClientConfigurationError, ServerError, InteractionRequiredError\n     */\n    public fromBridgeError(\n        error: unknown\n    ):\n        | AuthError\n        | ClientAuthError\n        | ClientConfigurationError\n        | ServerError\n        | InteractionRequiredAuthError {\n        if (isBridgeError(error)) {\n            switch (error.status) {\n                case BridgeStatusCode.UserCancel:\n                    return new ClientAuthError(\n                        ClientAuthErrorCodes.userCanceled\n                    );\n                case BridgeStatusCode.NoNetwork:\n                    return new ClientAuthError(\n                        ClientAuthErrorCodes.noNetworkConnectivity\n                    );\n                case BridgeStatusCode.AccountUnavailable:\n                    return new ClientAuthError(\n                        ClientAuthErrorCodes.noAccountFound\n                    );\n                case BridgeStatusCode.Disabled:\n                    return new ClientAuthError(\n                        ClientAuthErrorCodes.nestedAppAuthBridgeDisabled\n                    );\n                case BridgeStatusCode.NestedAppAuthUnavailable:\n                    return new ClientAuthError(\n                        error.code ||\n                            ClientAuthErrorCodes.nestedAppAuthBridgeDisabled,\n                        error.description\n                    );\n                case BridgeStatusCode.TransientError:\n                case BridgeStatusCode.PersistentError:\n                    return new ServerError(error.code, error.description);\n                case BridgeStatusCode.UserInteractionRequired:\n                    return new InteractionRequiredAuthError(\n                        error.code,\n                        error.description\n                    );\n                default:\n                    return new AuthError(error.code, error.description);\n            }\n        } else {\n            return new AuthError(\"unknown_error\", \"An unknown error occurred\");\n        }\n    }\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { BridgeStatusCode } from \"./BridgeStatusCode\";\n\nexport type BridgeError = {\n    status: BridgeStatusCode;\n    code?: string; // auth_flow_last_error such as invalid_grant\n    subError?: string; // server_suberror_code such as consent_required\n    description?: string;\n    properties?: object; // additional telemetry info\n};\n\nexport function isBridgeError(error: unknown): error is BridgeError {\n    return (error as BridgeError).status !== undefined;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { AuthError } from \"@azure/msal-common\";\n\n/**\n * NestedAppAuthErrorMessage class containing string constants used by error codes and messages.\n */\nexport const NestedAppAuthErrorMessage = {\n    unsupportedMethod: {\n        code: \"unsupported_method\",\n        desc: \"The PKCE code challenge and verifier could not be generated.\",\n    },\n};\n\nexport class NestedAppAuthError extends AuthError {\n    constructor(errorCode: string, errorMessage?: string) {\n        super(errorCode, errorMessage);\n\n        Object.setPrototypeOf(this, NestedAppAuthError.prototype);\n        this.name = \"NestedAppAuthError\";\n    }\n\n    public static createUnsupportedError(): NestedAppAuthError {\n        return new NestedAppAuthError(\n            NestedAppAuthErrorMessage.unsupportedMethod.code,\n            NestedAppAuthErrorMessage.unsupportedMethod.desc\n        );\n    }\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport {\n    CommonAuthorizationUrlRequest,\n    CommonSilentFlowRequest,\n    PerformanceCallbackFunction,\n    AccountInfo,\n    Logger,\n    ICrypto,\n    IPerformanceClient,\n    DEFAULT_CRYPTO_IMPLEMENTATION,\n    PerformanceEvents,\n    AccountFilter,\n    TimeUtils,\n} from \"@azure/msal-common\";\nimport { ITokenCache } from \"../cache/ITokenCache\";\nimport { BrowserConfiguration } from \"../config/Configuration\";\nimport { INavigationClient } from \"../navigation/INavigationClient\";\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\nimport { EndSessionPopupRequest } from \"../request/EndSessionPopupRequest\";\nimport { EndSessionRequest } from \"../request/EndSessionRequest\";\nimport { PopupRequest } from \"../request/PopupRequest\";\nimport { RedirectRequest } from \"../request/RedirectRequest\";\nimport { SilentRequest } from \"../request/SilentRequest\";\nimport { SsoSilentRequest } from \"../request/SsoSilentRequest\";\nimport {\n    ApiId,\n    WrapperSKU,\n    InteractionType,\n    DEFAULT_REQUEST,\n} from \"../utils/BrowserConstants\";\nimport { IController } from \"./IController\";\nimport { TeamsAppOperatingContext } from \"../operatingcontext/TeamsAppOperatingContext\";\nimport { IBridgeProxy } from \"../naa/IBridgeProxy\";\nimport { CryptoOps } from \"../crypto/CryptoOps\";\nimport { NestedAppAuthAdapter } from \"../naa/mapping/NestedAppAuthAdapter\";\nimport { NestedAppAuthError } from \"../error/NestedAppAuthError\";\nimport { EventHandler } from \"../event/EventHandler\";\nimport { EventType } from \"../event/EventType\";\nimport { EventCallbackFunction, EventError } from \"../event/EventMessage\";\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\nimport { BrowserCacheManager } from \"../cache/BrowserCacheManager\";\nimport { ClearCacheRequest } from \"../request/ClearCacheRequest\";\n\nexport class NestedAppAuthController implements IController {\n    // OperatingContext\n    protected readonly operatingContext: TeamsAppOperatingContext;\n\n    // BridgeProxy\n    protected readonly bridgeProxy: IBridgeProxy;\n\n    // Crypto interface implementation\n    protected readonly browserCrypto: ICrypto;\n\n    // Input configuration by developer/user\n    protected readonly config: BrowserConfiguration;\n\n    // Logger\n    protected logger: Logger;\n\n    // Performance telemetry client\n    protected readonly performanceClient: IPerformanceClient;\n\n    // EventHandler\n    protected readonly eventHandler: EventHandler;\n\n    // NestedAppAuthAdapter\n    protected readonly nestedAppAuthAdapter: NestedAppAuthAdapter;\n\n    constructor(operatingContext: TeamsAppOperatingContext) {\n        this.operatingContext = operatingContext;\n        const proxy = this.operatingContext.getBridgeProxy();\n        if (proxy !== undefined) {\n            this.bridgeProxy = proxy;\n        } else {\n            throw new Error(\"unexpected: bridgeProxy is undefined\");\n        }\n\n        // Set the configuration.\n        this.config = operatingContext.getConfig();\n\n        // Initialize logger\n        this.logger = this.operatingContext.getLogger();\n        // Initialize performance client\n        this.performanceClient = this.config.telemetry.client;\n\n        // Initialize the crypto class.\n        this.browserCrypto = operatingContext.isBrowserEnvironment()\n            ? new CryptoOps(this.logger, this.performanceClient)\n            : DEFAULT_CRYPTO_IMPLEMENTATION;\n\n        this.eventHandler = new EventHandler(this.logger, this.browserCrypto);\n\n        this.nestedAppAuthAdapter = new NestedAppAuthAdapter(\n            this.config.auth.clientId,\n            this.config.auth.clientCapabilities,\n            this.browserCrypto,\n            this.logger\n        );\n    }\n    getBrowserStorage(): BrowserCacheManager {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    getEventHandler(): EventHandler {\n        return this.eventHandler;\n    }\n\n    static async createController(\n        operatingContext: TeamsAppOperatingContext\n    ): Promise<IController> {\n        const controller = new NestedAppAuthController(operatingContext);\n        return Promise.resolve(controller);\n    }\n\n    initialize(): Promise<void> {\n        // do nothing not required by this controller\n        return Promise.resolve();\n    }\n\n    private ensureValidRequest<\n        T extends\n            | SsoSilentRequest\n            | SilentRequest\n            | PopupRequest\n            | RedirectRequest\n    >(request: T): T {\n        if (request?.correlationId) {\n            return request;\n        }\n        return {\n            ...request,\n            correlationId: this.browserCrypto.createNewGuid(),\n        };\n    }\n\n    private async acquireTokenInteractive(\n        request: PopupRequest | RedirectRequest\n    ): Promise<AuthenticationResult> {\n        const validRequest = this.ensureValidRequest(request);\n\n        this.eventHandler.emitEvent(\n            EventType.ACQUIRE_TOKEN_START,\n            InteractionType.Popup,\n            validRequest\n        );\n\n        const atPopupMeasurement = this.performanceClient.startMeasurement(\n            PerformanceEvents.AcquireTokenPopup,\n            validRequest.correlationId\n        );\n\n        atPopupMeasurement?.add({ nestedAppAuthRequest: true });\n\n        try {\n            const naaRequest =\n                this.nestedAppAuthAdapter.toNaaTokenRequest(validRequest);\n            const reqTimestamp = TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenInteractive(\n                naaRequest\n            );\n            const result: AuthenticationResult =\n                this.nestedAppAuthAdapter.fromNaaTokenResponse(\n                    naaRequest,\n                    response,\n                    reqTimestamp\n                );\n\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(\n                EventType.ACQUIRE_TOKEN_SUCCESS,\n                InteractionType.Popup,\n                result\n            );\n\n            atPopupMeasurement.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n\n            atPopupMeasurement.end({\n                success: true,\n                requestId: result.requestId,\n            });\n\n            return result;\n        } catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(\n                EventType.ACQUIRE_TOKEN_FAILURE,\n                InteractionType.Popup,\n                null,\n                e as EventError\n            );\n\n            atPopupMeasurement.end(\n                {\n                    success: false,\n                },\n                e\n            );\n\n            throw error;\n        }\n    }\n\n    private async acquireTokenSilentInternal(\n        request: SilentRequest\n    ): Promise<AuthenticationResult> {\n        const validRequest = this.ensureValidRequest(request);\n        this.eventHandler.emitEvent(\n            EventType.ACQUIRE_TOKEN_START,\n            InteractionType.Silent,\n            validRequest\n        );\n\n        const ssoSilentMeasurement = this.performanceClient.startMeasurement(\n            PerformanceEvents.SsoSilent,\n            validRequest.correlationId\n        );\n\n        ssoSilentMeasurement?.increment({\n            visibilityChangeCount: 0,\n        });\n\n        ssoSilentMeasurement?.add({\n            nestedAppAuthRequest: true,\n        });\n\n        try {\n            const naaRequest =\n                this.nestedAppAuthAdapter.toNaaTokenRequest(validRequest);\n            const reqTimestamp = TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenSilent(naaRequest);\n\n            const result: AuthenticationResult =\n                this.nestedAppAuthAdapter.fromNaaTokenResponse(\n                    naaRequest,\n                    response,\n                    reqTimestamp\n                );\n\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(\n                EventType.ACQUIRE_TOKEN_SUCCESS,\n                InteractionType.Silent,\n                result\n            );\n            ssoSilentMeasurement?.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n            ssoSilentMeasurement?.end({\n                success: true,\n                requestId: result.requestId,\n            });\n            return result;\n        } catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(\n                EventType.ACQUIRE_TOKEN_FAILURE,\n                InteractionType.Silent,\n                null,\n                e as EventError\n            );\n            ssoSilentMeasurement?.end(\n                {\n                    success: false,\n                },\n                e\n            );\n            throw error;\n        }\n    }\n\n    async acquireTokenPopup(\n        request: PopupRequest\n    ): Promise<AuthenticationResult> {\n        return this.acquireTokenInteractive(request);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenRedirect(request: RedirectRequest): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    async acquireTokenSilent(\n        silentRequest: SilentRequest\n    ): Promise<AuthenticationResult> {\n        return this.acquireTokenSilentInternal(silentRequest);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenByCode(\n        request: AuthorizationCodeRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<AuthenticationResult> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenNative(\n        request: // eslint-disable-line @typescript-eslint/no-unused-vars\n        | SilentRequest\n            | Partial<\n                  Omit<\n                      CommonAuthorizationUrlRequest,\n                      | \"requestedClaimsHash\"\n                      | \"responseMode\"\n                      | \"codeChallenge\"\n                      | \"codeChallengeMethod\"\n                      | \"nativeBroker\"\n                  >\n              >\n            | PopupRequest,\n        apiId: ApiId, // eslint-disable-line @typescript-eslint/no-unused-vars\n        accountId?: string | undefined // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<AuthenticationResult> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenByRefreshToken(\n        commonRequest: CommonSilentFlowRequest, // eslint-disable-line @typescript-eslint/no-unused-vars\n        silentRequest: SilentRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<AuthenticationResult> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    /**\n     * Adds event callbacks to array\n     * @param callback\n     */\n    addEventCallback(callback: EventCallbackFunction): string | null {\n        return this.eventHandler.addEventCallback(callback);\n    }\n\n    /**\n     * Removes callback with provided id from callback array\n     * @param callbackId\n     */\n    removeEventCallback(callbackId: string): void {\n        this.eventHandler.removeEventCallback(callbackId);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    addPerformanceCallback(callback: PerformanceCallbackFunction): string {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    removePerformanceCallback(callbackId: string): boolean {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    enableAccountStorageEvents(): void {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    disableAccountStorageEvents(): void {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getAccount(accountFilter: AccountFilter): AccountInfo | null {\n        throw NestedAppAuthError.createUnsupportedError();\n        // TODO: Look at standard implementation\n    }\n\n    getAccountByHomeId(homeAccountId: string): AccountInfo | null {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.homeAccountId === homeAccountId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(\n                    currentAccount\n                );\n            } else {\n                return null;\n            }\n        } else {\n            return null;\n        }\n    }\n\n    getAccountByLocalId(localId: string): AccountInfo | null {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.localAccountId === localId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(\n                    currentAccount\n                );\n            } else {\n                return null;\n            }\n        } else {\n            return null;\n        }\n    }\n\n    getAccountByUsername(userName: string): AccountInfo | null {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.username === userName) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(\n                    currentAccount\n                );\n            } else {\n                return null;\n            }\n        } else {\n            return null;\n        }\n    }\n    getAllAccounts(): AccountInfo[] {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return [\n                this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount),\n            ];\n        } else {\n            return [];\n        }\n    }\n    handleRedirectPromise(\n        hash?: string | undefined // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<AuthenticationResult | null> {\n        return Promise.resolve(null);\n    }\n    loginPopup(\n        request?: PopupRequest | undefined // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<AuthenticationResult> {\n        return this.acquireTokenInteractive(request || DEFAULT_REQUEST);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    loginRedirect(request?: RedirectRequest | undefined): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    logout(logoutRequest?: EndSessionRequest | undefined): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    logoutRedirect(\n        logoutRequest?: EndSessionRequest | undefined // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    logoutPopup(\n        logoutRequest?: EndSessionPopupRequest | undefined // eslint-disable-line @typescript-eslint/no-unused-vars\n    ): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n    ssoSilent(\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        request: Partial<\n            Omit<\n                CommonAuthorizationUrlRequest,\n                | \"requestedClaimsHash\"\n                | \"responseMode\"\n                | \"codeChallenge\"\n                | \"codeChallengeMethod\"\n                | \"nativeBroker\"\n            >\n        >\n    ): Promise<AuthenticationResult> {\n        return this.acquireTokenSilentInternal(request as SilentRequest);\n    }\n    getTokenCache(): ITokenCache {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    /**\n     * Returns the logger instance\n     */\n    public getLogger(): Logger {\n        return this.logger;\n    }\n\n    /**\n     * Replaces the default logger set in configurations with new Logger with new configurations\n     * @param logger Logger instance\n     */\n    setLogger(logger: Logger): void {\n        this.logger = logger;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setActiveAccount(account: AccountInfo | null): void {\n        /*\n         * StandardController uses this to allow the developer to set the active account\n         * in the nested app auth scenario the active account is controlled by the app hosting the nested app\n         */\n        this.logger.warning(\"nestedAppAuth does not support setActiveAccount\");\n        return;\n    }\n    getActiveAccount(): AccountInfo | null {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n        } else {\n            return null;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    initializeWrapperLibrary(sku: WrapperSKU, version: string): void {\n        /*\n         * Standard controller uses this to set the sku and version of the wrapper library in the storage\n         * we do nothing here\n         */\n        return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setNavigationClient(navigationClient: INavigationClient): void {\n        this.logger.warning(\n            \"setNavigationClient is not supported in nested app auth\"\n        );\n    }\n    getConfiguration(): BrowserConfiguration {\n        return this.config;\n    }\n    isBrowserEnv(): boolean {\n        return this.operatingContext.isBrowserEnvironment();\n    }\n    getBrowserCrypto(): ICrypto {\n        return this.browserCrypto;\n    }\n    getPerformanceClient(): IPerformanceClient {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    getRedirectResponse(): Map<string, Promise<AuthenticationResult | null>> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async clearCache(logoutRequest?: ClearCacheRequest): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async hydrateCache(\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        result: AuthenticationResult,\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        request:\n            | SilentRequest\n            | SsoSilentRequest\n            | RedirectRequest\n            | PopupRequest\n    ): Promise<void> {\n        throw NestedAppAuthError.createUnsupportedError();\n    }\n}\n"], "names": ["BridgeStatusCode", "NestedAppAuthAdapter", "constructor", "clientId", "clientCapabilities", "crypto", "logger", "this", "toNaaTokenRequest", "request", "_request$account", "extraParams", "undefined", "extraQueryParameters", "Map", "Object", "entries", "claims", "RequestParameterBuilder", "addClientCapabilitiesToClaims", "scopes", "OIDC_DEFAULT_SCOPES", "platformBrokerId", "account", "homeAccountId", "authority", "scope", "join", "correlationId", "createNewGuid", "StringUtils", "isEmptyObj", "state", "authenticationScheme", "AuthenticationScheme", "BEARER", "extraParameters", "fromNaaTokenResponse", "response", "reqTimestamp", "token", "id_token", "access_token", "createClientAuthError", "ClientAuthErrorCodes", "expiresOn", "Date", "expires_in", "idTokenClaims", "AuthToken", "base64Decode", "fromNaaAccountInfo", "environment", "uniqueId", "localAccountId", "tenantId", "split", "idToken", "accessToken", "fromCache", "tokenType", "extExpiresOn", "fromA<PERSON>unt", "effectiveIdTokenClaims", "oid", "sub", "tid", "concat", "username", "preferred_username", "name", "fromBridgeError", "error", "status", "isBridgeError", "<PERSON>th<PERSON><PERSON><PERSON>", "ClientAuthError", "code", "description", "ServerError", "InteractionRequiredAuthError", "NestedAppAuthErrorMessage", "desc", "NestedAppAuthError", "errorCode", "errorMessage", "super", "setPrototypeOf", "prototype", "createUnsupportedError", "NestedAppAuthController", "operatingContext", "proxy", "getBridgeProxy", "Error", "bridgeProxy", "config", "getConfig", "<PERSON><PERSON><PERSON><PERSON>", "performanceClient", "telemetry", "client", "browserCrypto", "isBrowserEnvironment", "CryptoOps", "DEFAULT_CRYPTO_IMPLEMENTATION", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "nestedAppAuthAdapter", "auth", "getBrowserStorage", "getEventHandler", "createController", "controller", "Promise", "resolve", "initialize", "ensureValidRequest", "acquireTokenInteractive", "validRequest", "emitEvent", "EventType", "ACQUIRE_TOKEN_START", "InteractionType", "Popup", "atPopupMeasurement", "startMeasurement", "PerformanceEvents", "AcquireTokenPopup", "add", "nestedAppAuthRequest", "naaRequest", "TimeUtils", "getTokenInteractive", "result", "setActiveAccount", "ACQUIRE_TOKEN_SUCCESS", "accessTokenSize", "length", "idTokenSize", "end", "success", "requestId", "e", "ACQUIRE_TOKEN_FAILURE", "acquireTokenSilentInternal", "Silent", "ssoSilentMeasurement", "SsoSilent", "increment", "visibilityChangeCount", "getTokenSilent", "acquireTokenPopup", "acquireTokenRedirect", "acquireTokenSilent", "silentRequest", "acquireTokenByCode", "acquireTokenNative", "apiId", "accountId", "acquireTokenByRefreshToken", "commonRequest", "addEventCallback", "callback", "removeEventCallback", "callbackId", "addPerformanceCallback", "removePerformanceCallback", "enableAccountStorageEvents", "disableAccountStorageEvents", "getAccount", "accountFilter", "getAccountByHomeId", "currentAccount", "getActiveAccount", "getAccountByLocalId", "localId", "getAccountByUsername", "userName", "getAllAccounts", "handleRedirectPromise", "hash", "loginPopup", "DEFAULT_REQUEST", "loginRedirect", "logout", "logoutRequest", "logoutRedirect", "logoutPopup", "ssoSilent", "getTokenCache", "<PERSON><PERSON><PERSON><PERSON>", "warning", "initializeWrapperLibrary", "sku", "version", "setNavigationClient", "navigationClient", "getConfiguration", "isBrowserEnv", "getBrowserCrypto", "getPerformanceClient", "getRedirectResponse", "clearCache", "hydrateCache"], "sourceRoot": ""}