/*! For license information please see 839.a4f0d10b.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunksokoon=self.webpackChunksokoon||[]).push([[839],{839:(e,t,r)=>{r.r(t),r.d(t,{NestedAppAuthController:()=>S});var n=r(290),o=r(2346),i=r(6378),s=r(4190),c=r(2625),a=r(6851),u=r(825),d=r(8051),l=r(2200),p=r(3932),h=r(7232),A=r(9632),g=r(7561),v=r(4430);const E="USER_INTERACTION_REQUIRED",m="USER_CANCEL",k="NO_NETWORK",C="TRANSIENT_ERROR",w="PERSISTENT_ERROR",I="DISABLED",T="ACCOUNT_UNAVAILABLE",f="NESTED_APP_AUTH_UNAVAILABLE";class R{constructor(e,t,r,n){this.clientId=e,this.clientCapabilities=t,this.crypto=r,this.logger=n}toNaaTokenRequest(e){var t;let r;r=void 0===e.extraQueryParameters?new Map:new Map(Object.entries(e.extraQueryParameters));const n=(new a.H).addClientCapabilitiesToClaims(e.claims,this.clientCapabilities),o=e.scopes||u.f_;return{platformBrokerId:null===(t=e.account)||void 0===t?void 0:t.homeAccountId,clientId:this.clientId,authority:e.authority,scope:o.join(" "),correlationId:void 0!==e.correlationId?e.correlationId:this.crypto.createNewGuid(),claims:d.x.isEmptyObj(n)?void 0:n,state:e.state,authenticationScheme:e.authenticationScheme||u.hO.BEARER,extraParameters:r}}fromNaaTokenResponse(e,t,r){if(!t.token.id_token||!t.token.access_token)throw(0,l.zP)(p.PM);const n=new Date(1e3*(r+(t.token.expires_in||0))),o=h.Z_(t.token.id_token,this.crypto.base64Decode),i=this.fromNaaAccountInfo(t.account,o),s=t.token.scope||e.scope;return{authority:t.token.authority||i.environment,uniqueId:i.localAccountId,tenantId:i.tenantId,scopes:s.split(" "),account:i,idToken:t.token.id_token,idTokenClaims:o,accessToken:t.token.access_token,fromCache:!0,expiresOn:n,tokenType:e.authenticationScheme||u.hO.BEARER,correlationId:e.correlationId,extExpiresOn:n,state:e.state}}fromNaaAccountInfo(e,t){const r=t||e.idTokenClaims,n=e.localAccountId||(null===r||void 0===r?void 0:r.oid)||(null===r||void 0===r?void 0:r.sub)||"",o=e.tenantId||(null===r||void 0===r?void 0:r.tid)||"",i=e.homeAccountId||"".concat(n,".").concat(o),s=e.username||(null===r||void 0===r?void 0:r.preferred_username)||"",c=e.name||(null===r||void 0===r?void 0:r.name);return{homeAccountId:i,environment:e.environment,tenantId:o,username:s,localAccountId:n,name:c,idToken:e.idToken,idTokenClaims:r}}fromBridgeError(e){if(!function(e){return void 0!==e.status}(e))return new v.l4("unknown_error","An unknown error occurred");switch(e.status){case m:return new l.er(p.$R);case k:return new l.er(p.Mq);case T:return new l.er(p.cX);case I:return new l.er(p.Ls);case f:return new l.er(e.code||p.Ls,e.description);case C:case w:return new A.n(e.code,e.description);case E:return new g.Yo(e.code,e.description);default:return new v.l4(e.code,e.description)}}}const y={code:"unsupported_method",desc:"The PKCE code challenge and verifier could not be generated."};class _ extends v.l4{constructor(e,t){super(e,t),Object.setPrototypeOf(this,_.prototype),this.name="NestedAppAuthError"}static createUnsupportedError(){return new _(y.code,y.desc)}}var N=r(4270),U=r(3057);class S{constructor(e){this.operatingContext=e;const t=this.operatingContext.getBridgeProxy();if(void 0===t)throw new Error("unexpected: bridgeProxy is undefined");this.bridgeProxy=t,this.config=e.getConfig(),this.logger=this.operatingContext.getLogger(),this.performanceClient=this.config.telemetry.client,this.browserCrypto=e.isBrowserEnvironment()?new c.Q(this.logger,this.performanceClient):n.d,this.eventHandler=new N.b(this.logger,this.browserCrypto),this.nestedAppAuthAdapter=new R(this.config.auth.clientId,this.config.auth.clientCapabilities,this.browserCrypto,this.logger)}getBrowserStorage(){throw _.createUnsupportedError()}getEventHandler(){return this.eventHandler}static async createController(e){const t=new S(e);return Promise.resolve(t)}initialize(){return Promise.resolve()}ensureValidRequest(e){return null!==e&&void 0!==e&&e.correlationId?e:{...e,correlationId:this.browserCrypto.createNewGuid()}}async acquireTokenInteractive(e){const t=this.ensureValidRequest(e);this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_START,s.s_.Popup,t);const r=this.performanceClient.startMeasurement(o.Ak.AcquireTokenPopup,t.correlationId);null===r||void 0===r||r.add({nestedAppAuthRequest:!0});try{const e=this.nestedAppAuthAdapter.toNaaTokenRequest(t),n=i.HY(),o=await this.bridgeProxy.getTokenInteractive(e),c=this.nestedAppAuthAdapter.fromNaaTokenResponse(e,o,n);return this.operatingContext.setActiveAccount(c.account),this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_SUCCESS,s.s_.Popup,c),r.add({accessTokenSize:c.accessToken.length,idTokenSize:c.idToken.length}),r.end({success:!0,requestId:c.requestId}),c}catch(n){const e=this.nestedAppAuthAdapter.fromBridgeError(n);throw this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_FAILURE,s.s_.Popup,null,n),r.end({success:!1},n),e}}async acquireTokenSilentInternal(e){const t=this.ensureValidRequest(e);this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_START,s.s_.Silent,t);const r=this.performanceClient.startMeasurement(o.Ak.SsoSilent,t.correlationId);null===r||void 0===r||r.increment({visibilityChangeCount:0}),null===r||void 0===r||r.add({nestedAppAuthRequest:!0});try{const e=this.nestedAppAuthAdapter.toNaaTokenRequest(t),n=i.HY(),o=await this.bridgeProxy.getTokenSilent(e),c=this.nestedAppAuthAdapter.fromNaaTokenResponse(e,o,n);return this.operatingContext.setActiveAccount(c.account),this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_SUCCESS,s.s_.Silent,c),null===r||void 0===r||r.add({accessTokenSize:c.accessToken.length,idTokenSize:c.idToken.length}),null===r||void 0===r||r.end({success:!0,requestId:c.requestId}),c}catch(n){const e=this.nestedAppAuthAdapter.fromBridgeError(n);throw this.eventHandler.emitEvent(U.t.ACQUIRE_TOKEN_FAILURE,s.s_.Silent,null,n),null===r||void 0===r||r.end({success:!1},n),e}}async acquireTokenPopup(e){return this.acquireTokenInteractive(e)}acquireTokenRedirect(e){throw _.createUnsupportedError()}async acquireTokenSilent(e){return this.acquireTokenSilentInternal(e)}acquireTokenByCode(e){throw _.createUnsupportedError()}acquireTokenNative(e,t,r){throw _.createUnsupportedError()}acquireTokenByRefreshToken(e,t){throw _.createUnsupportedError()}addEventCallback(e){return this.eventHandler.addEventCallback(e)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){throw _.createUnsupportedError()}removePerformanceCallback(e){throw _.createUnsupportedError()}enableAccountStorageEvents(){throw _.createUnsupportedError()}disableAccountStorageEvents(){throw _.createUnsupportedError()}getAccount(e){throw _.createUnsupportedError()}getAccountByHomeId(e){const t=this.operatingContext.getActiveAccount();return void 0!==t&&t.homeAccountId===e?this.nestedAppAuthAdapter.fromNaaAccountInfo(t):null}getAccountByLocalId(e){const t=this.operatingContext.getActiveAccount();return void 0!==t&&t.localAccountId===e?this.nestedAppAuthAdapter.fromNaaAccountInfo(t):null}getAccountByUsername(e){const t=this.operatingContext.getActiveAccount();return void 0!==t&&t.username===e?this.nestedAppAuthAdapter.fromNaaAccountInfo(t):null}getAllAccounts(){const e=this.operatingContext.getActiveAccount();return void 0!==e?[this.nestedAppAuthAdapter.fromNaaAccountInfo(e)]:[]}handleRedirectPromise(e){return Promise.resolve(null)}loginPopup(e){return this.acquireTokenInteractive(e||s.yo)}loginRedirect(e){throw _.createUnsupportedError()}logout(e){throw _.createUnsupportedError()}logoutRedirect(e){throw _.createUnsupportedError()}logoutPopup(e){throw _.createUnsupportedError()}ssoSilent(e){return this.acquireTokenSilentInternal(e)}getTokenCache(){throw _.createUnsupportedError()}getLogger(){return this.logger}setLogger(e){this.logger=e}setActiveAccount(e){this.logger.warning("nestedAppAuth does not support setActiveAccount")}getActiveAccount(){const e=this.operatingContext.getActiveAccount();return void 0!==e?this.nestedAppAuthAdapter.fromNaaAccountInfo(e):null}initializeWrapperLibrary(e,t){}setNavigationClient(e){this.logger.warning("setNavigationClient is not supported in nested app auth")}getConfiguration(){return this.config}isBrowserEnv(){return this.operatingContext.isBrowserEnvironment()}getBrowserCrypto(){return this.browserCrypto}getPerformanceClient(){throw _.createUnsupportedError()}getRedirectResponse(){throw _.createUnsupportedError()}async clearCache(e){throw _.createUnsupportedError()}async hydrateCache(e,t){throw _.createUnsupportedError()}}}}]);
//# sourceMappingURL=839.a4f0d10b.chunk.js.map