const express = require("express");
const app = express();
const fs = require('fs')
const Connection = require("./connection/connection");
const sequelize = require("sequelize");
const session = require('express-session');
const { links } = require('./models/models');
const dotenv = require("dotenv");
const server = require("http").createServer(app);
//Pass the Express app into the HTTP module.
const { Server } = require("socket.io");
const validate = require('validate-azure-ad-token').default;
const { useAzureSocketIO } = require("@azure/web-pubsub-socket.io");
// Body parser middleware
const bodyParser = require("body-parser");
let io = new Server(server);

// Use the following line to integrate with Web PubSub for Socket.IO
// useAzureSocketIO(io, {
//     hub: "notification", // The hub name can be any valid string.
//     connectionString: "Endpoint=https://new-sokoon-pubsub.webpubsub.azure.com;AccessKey=5k5solAkSGZZHpQfi9MFeV1gjJEDe2eTQ4SQAzyeTvc=;Version=1.0;"
// });
// Get Azure Web PubSub configuration from environment variables
const azurePubSubConnectionString = process.env.AZURE_PUBSUB_CONNECTION_STRING;

if (!azurePubSubConnectionString) {
    console.error('WARNING: AZURE_PUBSUB_CONNECTION_STRING not found in environment variables');
    console.error('Real-time notifications will not work. Please set AZURE_PUBSUB_CONNECTION_STRING in your .env file');
} else {
    useAzureSocketIO(io, {
        hub: "notification", // The hub name can be any valid string.
        connectionString: azurePubSubConnectionString
    });
}

io.on("connection", (socket) => {
    // Sends a message to the client
    // socket.emit("hello", "world");

    // Receives a message from the client
    // socket.on("howdy", (arg) => {
    //     console.log(arg);   // Prints "stranger"
    // })
});

// Database connection
Connection.connection.authenticate()
  .then(() => {
    console.log("Database connected...");
    const server = app.listen(process.env.PORT || 8080, () =>
      console.log(`Server is listening at port ${server.address().port}`)
    );
  })
  .catch((err) => {
    console.error("Error connecting to the database:", err);
});


app.set('trust proxy', true);
//convert into json
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

const morgan = require('morgan');
morgan.token('req-headers', function(req,res){
 return 'app-client-id: ' + req.headers['app-client-id']
})

app.use(morgan(':method :url :req-headers', {
	stream: fs.createWriteStream('./access.log', {flags: 'a'})
}));

app.use(express.static(__dirname + '/../../public'));

var path = require("path");
// CORS Configuration
var cors = require("cors");
const corsOption = {
  origin: true,
  credentials: true
  // methods: ['GET', 'POST', 'PUT', 'DELETE'], // Add other HTTP methods if needed
  // allowedHeaders: ['Content-Type', 'Authorization'], // Add other allowed headers
};

app.use(cors(corsOption)); // Use this after the variable declaration
dotenv.config();

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("An error occurred:", err);
  res.status(err.status || 500).json({
    message: err.message,
    errors: err.errors,
  });
  res.send({
    message: err.message,
    errors: err.errors,
  });
});

// app.get("/", async (req, res) => {
//   await Connection.connection.authenticate();
//   res.send("App Service connected to DB and running 🚀!");
// });


const crypto = require('crypto');

// Generate a unique ID for each link
function generateLinkId() {
  return crypto.randomBytes(8).toString('hex');
}

// Route to generate a shareable link
app.post('/api/generateLink', async (req, res) => {
  console.log('Received request body:', req.body);
  const mycontent = req.body.data; // Access content directly
  console.log('Received request to generate link with content:', mycontent);

  const linkId = generateLinkId();
  console.log('Generated link ID:', linkId);

  try {
    // Create a new link record in the database
    await links.create({
      content: mycontent,
      link: linkId
    });
    console.log('Link generated and saved to database:', linkId);

    // Generate deep links for Android and Universal Links for iOS
    const playStoreLink = generatePlayStoreLink(linkId);
    const appStoreLink = generateAppStoreLink(linkId);

    res.json({ link: `https://webapistg.msdf.gov.qa/sokoon/api/link/${linkId}`, playStoreLink, appStoreLink });
  } catch (err) {
    console.error('Error generating link:', err);
    res.status(500).json({ error: 'Error generating link' });
  }
});

// Route to get content from a shareable link
app.get('/api/link/:linkId', async (req, res) => {
  const { linkId } = req.params;
  console.log('Received request for link:', linkId);

  try {
    // Find the link record in the database by its ID
    const link = await links.findOne({ where: { link: linkId } });
    if (link) {
      console.log('Content found:', link.content);
      res.json({ content: link.content });
    } else {
      console.log('Link not found');
      res.status(404).json({ error: 'Link not found' });
    }
  } catch (err) {
    console.error('Error retrieving link:', err);
    res.status(500).json({ error: 'Error retrieving link' });
  }
});

// Function to generate a deep link for Android (Play Store)
function generatePlayStoreLink(linkId) {
  // Replace 'com.awas.sokoon' with the actual package name of your Android app
  const packageName = 'com.awas.sokoon';
  return `https://sokoon.page.link/words?id=${linkId}&referrer=myapp://screen/${linkId}`;
}

// Function to generate a Universal Link for iOS (App Store)
function generateAppStoreLink(linkId) {
  // Replace '1635583968' with the actual App Store ID of your iOS app
  const appId = '1635583968';
  return `https://sokoon.page.link/words?id=${linkId}&referrer=myapp://screen/${linkId}`;
}


//Routes
const adminRoutes = require("./routes/admins");
const locationsRoutes = require("./routes/locations");
const adRoutes = require("./routes/ads");
const categoriesRoutes = require("./routes/categories");
const favouritesRoutes = require("./routes/favourites");
const feedbacksRoutes = require("./routes/feedbacks");
const guidesRoutes = require("./routes/guides");
const newWordsRoutes = require("./routes/newWords");
const notificationsRoutes = require("./routes/notifications")(io);
const sentencesRoutes = require("./routes/sentences");
const settingsRoutes = require("./routes/settings");
const signsRoutes = require("./routes/signs");
const storiesRoutes = require("./routes/stories");
const updateWordsRoutes = require("./routes/updateWords");
const userGuidesRoutes = require("./routes/userGuides");
const usersRoutes = require("./routes/users");
const userSearchesRoutes = require("./routes/userSearches");
const wordsRoutes = require("./routes/words");
const partnersRoutes = require("./routes/partners");
const authRoutes = require("./routes/auth");
const { error } = require("console");



app.use("/api/admins", adminRoutes);
app.use("/api/locations", locationsRoutes);
app.use("/api/ads", adRoutes);
app.use("/api/categories", categoriesRoutes);
app.use("/api/favourites", favouritesRoutes);
app.use("/api/feedbacks", feedbacksRoutes);
app.use("/api/guides", guidesRoutes);
app.use("/api/newwords", newWordsRoutes);
app.use("/api/notifications", notificationsRoutes);
app.use("/api/sentences", sentencesRoutes);
app.use("/api/settings", settingsRoutes);
app.use("/api/signs", signsRoutes);
app.use("/api/stories", storiesRoutes);
app.use("/api/updatewords", updateWordsRoutes);
app.use("/api/userGuides", userGuidesRoutes);
app.use("/api/users", usersRoutes);
app.use("/api/userSearches", userSearchesRoutes);
app.use("/api/words", wordsRoutes);
app.use("/api/partners", partnersRoutes);
app.use("/api/aad/auth/openid/return", authRoutes);
app.get("/api/aad/auth/openid/return", authRoutes);

let pa = './static'
app.use(express.static(path.join(__dirname, pa), { maxAge: '1d' }));
app.use('/assets', express.static(path.join(__dirname, pa, '/assets'), { maxAge: '1d' }));

app.get('/', function (req, res) {
    res.sendFile(path.join(__dirname, pa, 'index.html'));
});
app.get('/*', function (req, res) {
    res.sendFile(path.join(__dirname, pa, 'index.html'));
});