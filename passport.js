const passport = require('passport');
const jwt = require('jsonwebtoken');
const bunyan = require('bunyan');
require('dotenv').config();

const OIDCStrategy = require('passport-azure-ad').OIDCStrategy;

const log = bunyan.createLogger({
    name: 'Microsoft OIDC Example Web Application'
});

// array to hold logged in users
var users = [];

var findByOid = function(oid, fn) {
  for (var i = 0, len = users.length; i < len; i++) {
    var user = users[i];
   log.info('we are using user: ', user);
    if (user.oid === oid) {
      return fn(null, user);
    }
  }
  return fn(null, null);
};
/******************************************************************************
 * Set up passport in the app
 ******************************************************************************/

//-----------------------------------------------------------------------------
// To support persistent login sessions, Passport needs to be able to
// serialize users into and deserialize users out of the session.  Typically,
// this will be as simple as storing the user ID when serializing, and finding
// the user by ID when deserializing.
//-----------------------------------------------------------------------------

passport.serializeUser((user, done) => {
    done(null, user);
});

passport.deserializeUser(function(oid, done) {
    findByOid(oid, function (err, user) {
      done(err, user);
    });
  });



// Validate required environment variables for B2C
const b2cClientId = process.env.B2C_CLIENT_ID;
const b2cClientSecret = process.env.B2C_CLIENT_SECRET;
const b2cTenantId = process.env.B2C_TENANT_ID;

if (!b2cClientId || !b2cClientSecret || !b2cTenantId) {
    console.error('CRITICAL: Azure B2C configuration missing from environment variables');
    console.error('Please set B2C_CLIENT_ID, B2C_CLIENT_SECRET, and B2C_TENANT_ID in your .env file');
    process.exit(1);
}

passport.use(new OIDCStrategy({
  policyName: 'B2C_1_sokoon',
  tenantName: b2cTenantId,
  //identityMetadata: 'https://MSDFGOVQAB2CStg.b2clogin.com/MSDFGOVQAB2CStg.onmicrosoft.com/v2.0/.well-known/openid-configuration?p=B2C_1_sokoon',
  identityMetadata: `https://login.microsoftonline.com/${b2cTenantId}/.well-known/openid-configuration?p=B2C_1_sokoon`,
  //identityMetadata: 'https://MSDFGOVQAB2CStg.b2clogin.com/MSDFGOVQAB2CStg.onmicrosoft.com/oauth2/v2.0/authorize?p=B2C_1_sokoon&client_id=5d9c02ca-d3a8-4981-930a-74ae56d428e3&nonce=defaultNonce&redirect_uri=https%3A%2F%2Fwebapp-sokoon-stgs-qc-001.azurewebsites.net%2Fapi%2Faad%2Fauth%2Fopenid%2Freturn&scope=openid&response_type=id_token&prompt=login',
  clientID: b2cClientId,
  clientSecret: b2cClientSecret,
  responseType: 'code id_token',
  responseMode: 'form_post',
  redirectUrl: 'https://webapp-sokoon-stg-qc-001.azurewebsites.net/api/aad/auth/openid/return',
  allowHttpForRedirectUrl: true,
  isB2C: true,
  validateIssuer: true,
  issuer: `https://${b2cTenantId.split('.')[0]}.b2clogin.com/58a8cb32-f133-4a83-be9e-98b9a4eb66f3/v2.0/`,
  passReqToCallback: true,
  useCookieInsteadOfSession: false,
  //cookieEncryptionKeys: [
   // { 'key': '12345678901234567890123456789012', 'iv': '123456789012' },
   // { 'key': 'abcdefghijklmnopqrstuvwxyzabcdef', 'iv': 'abcdefghijkl' }
  //],
  scope: ['openid', 'profile', 'offline_access', 'https://graph.microsoft.com/mail.read'],
  loggingLevel: 'info',
  loggingNoPII: false,
},
function(req, iss, sub, profile, accessToken, refreshToken, done){
  console.log("inside passport function");
    if (!profile.oid) {
      return done(new Error("No oid found"), null);
    }
    // asynchronous verification, for effect...
    process.nextTick(function () {
      findByOid(profile.oid, function(err, user) {
        if (err) {
          return done(err);
        }
        if (!user) {
          // "Auto-registration"
          users.push(profile);
          return done(null, profile);
        }
        return done(null, user);
      });
    });
  }
));

module.exports = passport;