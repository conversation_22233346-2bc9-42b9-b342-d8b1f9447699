{"name": "sokoon", "version": "1.0.0", "description": "Sokoon App Admin <PERSON>", "main": "app.js", "keywords": ["Sokoon", "admin", "backend"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@azure/core-tracing": "^1.1.2", "@azure/msal-browser": "^3.14.0", "@azure/msal-node": "^2.6.6", "@azure/msal-react": "^2.0.16", "@azure/storage-blob": "^12.17.0", "@azure/web-pubsub-socket.io": "^1.1.0", "@sendgrid/mail": "^8.1.0", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bunyan": "^1.8.15", "connect-redis": "^8.1.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "firebase-admin": "^12.1.0", "helmet": "^8.1.0", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "nodemon": "^3.0.2", "npm": "^10.6.0", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "passport-azure-ad-oauth2": "^0.0.4", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "randomstring": "^1.3.0", "redis": "^5.1.1", "restify": "^11.1.0", "sequelize": "^6.35.1", "socket.io": "^4.7.2", "socket.io-client": "^4.7.5", "twilio": "^4.19.0", "validate-azure-ad-token": "^2.2.0"}, "scripts": {"start": "node app.js"}}