const passport = require('passport');
const jwt = require('jsonwebtoken');
const bunyan = require('bunyan');
require('dotenv').config();

const AzureAdOAuth2Strategy = require('passport-azure-ad-oauth2');

const log = bunyan.createLogger({
    name: 'Microsoft OIDC Example Web Application'
});


/******************************************************************************
 * Set up passport in the app
 ******************************************************************************/

//-----------------------------------------------------------------------------
// To support persistent login sessions, Passport needs to be able to
// serialize users into and deserialize users out of the session.  Typically,
// this will be as simple as storing the user ID when serializing, and finding
// the user by ID when deserializing.
//-----------------------------------------------------------------------------

passport.serializeUser((user, done) => {
    done(null, user);
});

passport.deserializeUser(function(oid, done) {
    findByOid(oid, function (err, user) {
      done(err, user);
    });
  });


  passport.use(new AzureAdOAuth2Strategy({
    clientID: process.env.B2C_CLIENT_ID,
    clientSecret: process.env.B2C_CLIENT_SECRET,
    callbackURL: 'https://webapp-sokoon-stgs-qc-001.azurewebsites.net/api/aad/auth/openid/return',
    tenant: process.env.B2C_TENANT_ID,
  },
  function (accessToken, refresh_token, params, profile, done) {
    var waadProfile = profile || jwt.decode(params.id_token, '', true);

    User.findOrCreate({ id: waadProfile.upn }, function (err, user) {
      done(err, user);
    });
  }));


module.exports = passport;