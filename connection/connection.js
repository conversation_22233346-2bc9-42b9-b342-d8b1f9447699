const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
require('dotenv').config();
// Construct the path to the SSL certificate
const sslCertPath = path.join(__dirname, '..', 'certificate2.pem');
//ffdsad
// const connection = new Sequelize(
// //Azure Connection
//  "postgres",
//  "sokoon",
//  "InnovationCafe@48",

//   {
//    host: 'pgsql3-sokoon-stg-qc-001.postgres.database.azure.com',
//    dialect: "postgres",
//    port: '5432',

//     define: {
//       timestamps: false, //turnoff timestapm
//     },
//     dialectOptions: {
//       ssl: {
//         ca: fs.readFileSync(sslCertPath), // Path to the SSL public certificate
//         // Other SSL options can be added here if needed
//       }
//     },
//     pool: {
//       max: 5,
//       min: 1,
//       idle: 10000,
//     },
//   }
// );

// Testing Azure (ra)
// const connection = new Sequelize(
//   "postgres",
//   "pgadmin",
//   "DejU.gaRNbhzjP",
//  {
//    host: 'new-pg-db.postgres.database.azure.com',
//    port: "5432",
//    dialect: "postgres",

//    define: {
//      timestamps: false, //turnoff timestapm
//    },
//    dialectOptions: {
// 	      ssl: {
// 	        ca: fs.readFileSync('certificate2.pem'), // Path to the SSL public certificate
// 	        // Other SSL options can be added here if needed
// 	      }
// 	    },
//    pool: {
//      max: 3,
//      min: 1,
//      idle: 10000,
//    },
//  }
// );

const connection = new Sequelize(
	"sokoon",
	"sokoon",
	"InnovationCafe@48",
   {
	 host: 'pgsql3-sokoon-dev-qc-001.postgres.database.azure.com',
	 port: "5432",
	 dialect: "postgres",
	
	 define: {
	   timestamps: false, //turnoff timestapm
	 },
	 dialectOptions: {
			ssl: {
			  ca: fs.readFileSync(sslCertPath).toString(), // Path to the SSL public certificate
			  // Other SSL options can be added here if needed
			}
		  },
	 pool: {
	   max: 3,
	   min: 1,
	   idle: 10000,
	 },
   }
  );

// const connection = new Sequelize(
// 	"sokoon",
// 	"sokoon",
// 	"InnovationCafe@48",
//    {
// 	 host: 'pgsql3-sokoon-stg-qc-001.postgres.database.azure.com',
// 	 port: "5432",
// 	 dialect: "postgres",

// 	 define: {
// 	   timestamps: false, //turnoff timestapm
// 	 },
// 	 dialectOptions: {
// 			ssl: {
// 			  ca: fs.readFileSync(sslCertPath).toString(), // Path to the SSL public certificate
// 			  // Other SSL options can be added here if needed
// 			}
// 		  },
// 	 pool: {
// 	   max: 3,
// 	   min: 1,
// 	   idle: 10000,
// 	 },
//    }
//   );
// Get database configuration from environment variables
const dbName = process.env.DB_NAME;
const dbUser = process.env.DB_USER;
const dbPassword = process.env.DB_PASSWORD;
const dbHost = process.env.DB_HOST;
const dbPort = process.env.DB_PORT || "5432";

// Validate required environment variables
if (!dbName || !dbUser || !dbPassword || !dbHost) {
    console.error('CRITICAL: Database configuration missing from environment variables');
    console.error('Please set DB_NAME, DB_USER, DB_PASSWORD, and DB_HOST in your .env file');
    process.exit(1);
}

const connection = new Sequelize(
	dbName,
	dbUser,
	dbPassword,
   {
	 host: dbHost,
	 port: dbPort,
	 dialect: "postgres",

	 define: {
	   timestamps: false, //turnoff timestapm
	 },
	//  dialectOptions: {
	// 		ssl: {
	// 		  ca: fs.readFileSync('DigiCertGlobalRootCA.crt.pem'), // Path to the SSL public certificate
	// 		  // Other SSL options can be added here if needed
	// 		}
	// 	  },
	 pool: {
	   max: 3,
	   min: 1,
	   idle: 10000,
	 },
   }
  );
// const connection = new Sequelize('postgres://pgadmin:<EMAIL>:5432/postgres')

module.exports.connection = connection;
