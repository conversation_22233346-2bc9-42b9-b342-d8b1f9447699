# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- main

stages:
- stage: Build
  displayName: 'Build '
  jobs: 

  - job: build
    pool: vmss-agentspool-windows-hub-002
 
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        npm install
      displayName: 'npm install and build'
 
    - task: ArchiveFiles@2
      displayName: "Archive  UI"
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        replaceExistingArchive: true

    - task: PublishPipelineArtifact@1
      displayName: "Publish  UI Artifact"
      inputs:
        targetPath: '$(Build.ArtifactStagingDirectory)'
        artifact: 'drop_ui'
        publishLocation: 'pipeline'
         
         
