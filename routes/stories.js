const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");
const multer = require("multer");

//Get Single Story
router.get("/Get_SingleStory/:stry_id", (req, res, next) => {
    const { stry_id } = req.params;

    models.stories
        .findAll({
            where: {
                id: stry_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Commonly Used Sign Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Commonly Used Sign Get Successfully",
                });
            } else {
                console.log("No Commonly Used Sign Found");
                res.json({
                    successful: false,
                    message: "No Commonly Used Sign Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Common Used Sign: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Common Used Sign: " + err,
            });
        });
});

//Get All Stories
router.get("/Get_AllStories", (req, res, next) => {
    models.stories
        .findAll({
            order: [["titleEn", "ASC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Commonly Used Signs Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Commonly Used Signs Successfully",
                });
            } else {
                console.log("No Commonly Used Sign Found");
                res.json({
                    successful: false,
                    message: "No Commonly Used Sign Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Commonly Used Signs: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Commonly Used Signs: " + err,
            });
        });
});

// Create Story
router.post("/Create_Story", async (req, res, next) => {
    const { 
        thumbnail, 
        url, 
        titleEn, 
        titleAr, 
        titleFr, 
        is_active, 
        type, 
        categories 
    } = req.body.data;

    const values = {
        thumbnail: thumbnail,
        url: url,
        titleFr: titleFr,
        titleAr: titleAr,
        titleEn: titleEn,
        is_active: is_active,
        type: type,
        categories: categories,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the story already exists
        // const existingStory = await models.stories.findOne({
        //     where: {
        //         titleEn: titleEn,
        //     },
        // });

        // if (existingStory) {
        //     console.log("Sign already exists");
        //     return res.json({
        //         successful: false,
        //         message: "Sign already exists",
        //     });
        // }

        // Create a new story
        const newStory = await models.stories.create(values);

        if (newStory) {
            console.log("Sign Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Sign Created Successfully",
                    data: newStory,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "Sign Created Successfully",
                data: newStory,
            });
        }
    } catch (err) {
        console.log("Failed to Create New Story: ", err);
        return res.json({
            successful: false,
            message: "Failed to Create New Story: " + err,
        });
    }
});

//Update Story Detail
router.post("/Update_StoryDetail", async (req, res, next) => {
    console.log("Update Story Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            thumbnail: req.body.data.thumbnail,
            url: req.body.data.url,
            titleEn: req.body.data.titleEn,
            titleAr: req.body.data.titleAr,
            titleFr: req.body.data.titleFr,
            is_active: req.body.data.is_active,
            type: req.body.data.type,
            categories: req.body.data.categories,
        },
    ];
    await models.stories
        .update(
            {
                thumbnail: values[0].thumbnail,
                url: values[0].url,
                titleEn: values[0].titleEn,
                titleAr: values[0].titleAr,
                titleFr: values[0].titleFr,
                is_active: values[0].is_active,
                type: values[0].type,
                categories: values[0].categories,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Commonly Used Sign Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Story Status
router.post("/Update_Storiestatus", async (req, res, next) => {
    console.log("Update Story Status API calling", req.body.data);
    values = [
        {
            id: req.body.data.id,
            status: req.body.data.status,
        },
    ];
    await models.stories
        .update(
            {
                is_active: values[0].status,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const val = {
                id: values[0].id,
                is_active: values[0].status,
            };
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Commonly Used Sign Status Updated Successfully",
                    data: val,
                },
                accessTokenSecret
            );
            console.log("val", val);
            res.json({
                successful: true,
                message: "Successful",
                data: val,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Story Pic
router.post("/Update_StoryPic", async (req, res, next) => {
    console.log("Update Commonly Used Sign Pic API Calling", req.body.data);

    values = [
        {
            id: req.body.data.id,
            thumbnail: req.body.data.thumbnail,
        },
    ];
    await models.stories
        .update(
            {
                thumbnail: values[0].thumbnail,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Commonly Used Sign Pic Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Story Vid
router.post("/Update_StoryVid", async (req, res, next) => {
    console.log("Update Commonly Used Sign Vid API Calling", req.body.data);

    values = [
        {
            id: req.body.data.id,
            url: req.body.data.url,
        },
    ];
    await models.stories
        .update(
            {
                url: values[0].url,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Common Sign Video Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Setup Storage Folder
var storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, "./StoriesImages");
    },
    filename: function (req, file, cb) {
        cb(null, file.originalname);
    },
});

//Upload Story Pic
var upload = multer({ storage: storage }).single("file");
router.post("/StoryPic", function (req, res) {
    console.log("Req:", req);
    upload(req, res, function (err) {
        if (err instanceof multer.MulterError) {
            return res.json(err);
        } else if (err) {
            return res.json(err);
        }
        return res.send(req.file);
    });
});

//Upload Story Vid
var upload = multer({ storage: storage }).single("file");
router.post("/StoryVid", function (req, res) {
    console.log("Req:", req);
    upload(req, res, function (err) {
        if (err instanceof multer.MulterError) {
            return res.json(err);
        } else if (err) {
            return res.json(err);
        }
        return res.send(req.file);
    });
});

//Delete Single Story
router.get("/Delete_SingleStory/:id", (req, res, next) => {
    const { id } = req.params;

    models.stories
        .destroy({
            where: {
                id: id,
            },
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Commonly Used Sign Deleted Successfully.");
                res.json({
                    data: data,
                    successful: true,
                    message: "Commonly Used Sign Deleted Successfully.",
                });
            } else {
                console.log("No Commonly Used Sign Found");
                res.json({
                    successful: false,
                    message: "No Commonly Used Sign Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Delete Commonly Used Sign: ", err);
            res.json({
                successful: false,
                message: "Failed To Delete Commonly Used Sign: " + err,
            });
        });
});


module.exports = router;