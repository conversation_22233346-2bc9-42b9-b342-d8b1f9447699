const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;

// Get all countries
router.get("/countries", (req, res, next) => {
    console.log("Countries API calling");

    models.countries
        .findAll({})
        .then((c_data) => {
            if (c_data?.length !== 0 || c_data !== null) {
                console.log("Countries Get Successfully");

                // Add country_url to each country in the response
                const countriesWithUrls = c_data.map((country) => {
                    const countryUrl = `https://raw.githubusercontent.com/hampusborgos/country-flags/main/png250px/${country.sortname.toLowerCase()}.png`;
                    return {
                        ...country.toJSON(),
                        country_url: countryUrl,
                    };
                });

                res.status(200).json({
                    data: countriesWithUrls,
                    successful: true,
                    message: "Countries Get Successfully",
                });
            } else {
                console.log("No Countries Found");
                res.status(404).json({
                    successful: false,
                    message: "Countries not Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed to Get Countries: ", err);
            res.status(404).json({
                successful: false,
                message: "Failed to Get Countries: " + err,
            });
        });
});


// Get Country Name
router.get("/Get_Country/:id", (req, res, next) => {
    console.log("Get Country Name API calling: ", req.params.id);
    const { id } = req.params;

    models.countries
        .findAll({
            where: {
                id: id,
            },
        })
        .then((data) => {
            if (data?.length !== 0 || data !== null) {
                console.log("Country Name Get Successfully");

                // Assuming data is an array, get the first element
                const country = data[0];

                // Create the country URL using the lowercase sortname value
                const countryUrl = `https://raw.githubusercontent.com/hampusborgos/country-flags/main/png250px/${country.sortname.toLowerCase()}.png`;

                // Add the country_url to the response
                res.json({
                    data: {
                        ...country.toJSON(),
                        country_url: countryUrl,
                    },
                    successful: true,
                    message: "Country Name Get Successfully",
                });
            } else {
                console.log("No Country Found");
                res.json({
                    successful: false,
                    message: "No Country Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get Country Name: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Country Name: " + err,
            });
        });
});



//Get State Name
router.get("/Get_State/:id", (req, res, next) => {
    console.log("Get State Name API calling: ", req.params.id);
  const { id } = req.params;

  models.states
    .findAll({
      where: {
        id: id,
      },
    })

    .then((data) => {
      if (data?.length != 0 || data != null) {
        console.log("State Name Get Successfully");
        res.json({
          data: data,
          successful: true,
          message: "State Name Get Successfully",
        });
      } else {
        console.log("No State Found");
        res.json({
          successful: false,
          message: "No State Found",
        });
      }
    })

    .catch(function (err) {
      console.log("Failed To Get State Name: ", err);
      res.json({
        successful: false,
        message: "Failed To Get State Name: " + err,
      });
    });
});

//Get City Name
router.get("/Get_City/:id", (req, res, next) => {
    console.log("Get City Name API calling: ", req.params.id);
  const { id } = req.params;

  models.cities
    .findAll({
      where: {
        id: id,
      },
    })

    .then((data) => {
      if (data?.length != 0 || data != null) {
        console.log("City Name Get Successfully");
        res.json({
          data: data,
          successful: true,
          message: "City Name Get Successfully",
        });
      } else {
        console.log("No City Found");
        res.json({
          successful: false,
          message: "No City Found",
        });
      }
    })

    .catch(function (err) {
      console.log("Failed To Get City Name: ", err);
      res.json({
        successful: false,
        message: "Failed To Get City Name: " + err,
      });
    });
});

module.exports = router;