const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");
const multer = require("multer");
const { containerClient } = require('../storage');

//Get Single Setting
router.get("/Get_SingleSetting/:stg_id", (req, res, next) => {
    const { stg_id } = req.params;

    models.settings
        .findAll({
            where: {
                id: stg_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Setting Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Setting Get Successfully",
                });
            } else {
                console.log("No Setting Found");
                res.json({
                    successful: false,
                    message: "No Setting Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Setting: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Setting: " + err,
            });
        });
});

//Get All Settings
router.get("/Get_AllSettings", (req, res, next) => {
    models.settings
        .findAll({

        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Settings Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Settings Successfully",
                });
            } else {
                console.log("No Settings Found");
                res.json({
                    successful: false,
                    message: "No Settings Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Settings: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Settings: " + err,
            });
        });
});

// Create Setting
router.post("/Create_Setting", async (req, res, next) => {
    const { 
        titleEn, 
        titleAr, 
        titleFr, 
        descriptionEn, 
        descriptionAr, 
        descriptionFr, 
        image1 
    } = req.body.data;

    const values = {
        titleEn: titleEn,
        titleAr: titleAr,
        titleFr: titleFr,
        descriptionEn: descriptionEn,
        descriptionAr: descriptionAr,
        descriptionFr: descriptionFr,
        image1: image1,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the setting already exists
        const existingSetting = await models.settings.findOne({
            where: {
                titleAr: titleAr,
            },
        });

        if (existingSetting) {
            console.log("Setting already exists");
            return res.json({
                successful: false,
                message: "Setting already exists",
            });
        }

        // Create a new setting
        const newSetting = await models.settings.create(values);

        if (newSetting) {
            console.log("Setting Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Setting Created Successfully",
                    data: newSetting,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "Setting Created Successfully",
                data: newSetting,
            });
        }
    } catch (err) {
        console.log("Failed to Create New Setting: ", err);
        return res.json({
            successful: false,
            message: "Failed to Create New Setting: " + err,
        });
    }
});


//Update Setting Detail
router.post("/Update_SettingDetail", async (req, res, next) => {
    console.log("Update Setting Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
	    titleEn: req.body.data.titleEn,   
	    titleAr: req.body.data.titleAr,         
	    titleFr: req.body.data.titleFr,
            descriptionEn: req.body.data.descriptionEn,
            descriptionAr: req.body.data.descriptionAr,
            descriptionFr: req.body.data.descriptionFr,
            image1: req.body.data.image1,
        },
    ];
    await models.settings
        .update(
            {
                titleEn: values[0].titleEn,
		titleAr: values[0].titleAr,		
		titleFr: values[0].titleFr,
                descriptionEn: values[0].descriptionEn,
                descriptionAr: values[0].descriptionAr,
                descriptionFr: values[0].descriptionFr,
                image1: values[0].image1,
		updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Setting Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});


//Update Setting Pic1
router.post("/Update_SettingPic1", async (req, res, next) => {
    console.log("Update Setting Pic API Calling", req.body.data);

    const { id, imageUrl } = req.body.data; // Extract id and imageUrl from request body
  
    try {
      const updatedWord = await models.settings.findOneAndUpdate({ id: id }, // Find the word by its id
        {   
            thumbnail: imageUrl, 
            updated_at: new Date().toISOString() 
        }, // Update the thumbnail and updated_at fields
        { 
            returning: true 
        } // Return the updated word document
      );
  
      if (updatedWord) {
        const accessToken = jwt.sign(
          {
            successful: true,
            message: "Word Pic Updated Successfully",
            data: updatedWord,
          },
          accessTokenSecret
        );
        res.json({
          successful: true,
          message: "Successful",
          data: updatedWord,
          accessToken,
        });
      } else {
        res.status(404).json({
          message: "Word not found",
          successful: false,
        });
      }
    } catch (err) {
      console.log(err);
      res.status(500).json({
        message: "Failed to update word pic: " + err,
        successful: false,
      });
    }
  });


//Setup Storage Folder
// Define storage for multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Specify the directory where the uploaded files should be stored
    cb(null, 'uploads/'); // You need to create this 'uploads' directory
  },
  filename: function (req, file, cb) {
    // Use the original name of the file for storing
    cb(null, file.originalname);
  }
});

// Initialize multer with the defined storage
const upload = multer({ storage: storage });

router.post("/SettingPic1", upload.single('file'), async function (req, res) {
  try {
    console.log("Request received:", req);

    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }
    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // Set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Image URL:", imageUrl);

    res.json({ 
      imageUrl,
      filename: blobName
    });
  } catch (error) {
    console.log("Error uploading image:", error);
    res.status(500).json({ message: "Failed to upload image" });
  }
});




    module.exports = router;