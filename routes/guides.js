const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {accessTokenSecret,} = require("../config");
const { containerClient } = require('../storage');
const multer = require("multer");

router.get("Get_SingleGuide/:g_id", (req, res, next) => {
    const { g_id } = req.params;
    models.guides
        .findAll({
            where: {
                id: g_id,
            }, 
           
        })
        .then((data) => {
            if (data?.length !== 0) {
                console.log("Guide Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Guide Get Successfully",
                });
            } else {
                console.log("No Guide Found");
                res.json({
                    successful: false,
                    message: "No Guide Found",
                });
            }
        })
        .catch(function (err) {
         res.json({
            successful: false, 
             message: "Failed to get Guide" + err, 
         });
        });
      
});

router.get("/Get_AllGuides", (req, res, next) => {
    models.guides
        .findAll({
            order: [["created_at", "DESC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Guides Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Guides Successfully",
                });
            } else {
                console.log("No Guides Found");
                res.json({
                    successful: false,
                    message: "No Guides Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All  guides: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All  guides: " + err,
            });
        });
});

// Create Guide
router.post("/Create_Guide", async (req, res, next) => {
    const { pdfUrlAr, pdfUrlEn, imageEn, imageAr, is_active } = req.body.data;

    const values = {
        pdfUrlAr: pdfUrlAr,
        pdfUrlEn: pdfUrlEn,
        imageAr: imageAr,
        imageEn: imageEn,
        is_active: is_active,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the guide already exists
        const existingGuide = await models.guides.findOne({
            where: {
                pdfUrlEn: pdfUrlEn,
            },
        });

        if (existingGuide) {
            console.log("Guide already exists");
            return res.json({
                successful: false,
                message: "Guide already exists",
            });
        }

        // Create a new guide
        const newGuide = await models.guides.create(values);

        if (newGuide) {
            console.log("Guide Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Guide Created Successfully",
                    data: newGuide,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "Guide Created Successfully",
                data: newGuide,
            });
        }
    } catch (err) {
        console.log("Failed to Create New Guide: ", err);
        return res.json({
            successful: false,
            message: "Failed to Create New Guide: " + err,
        });
    }
});

//Update Guide Detail
router.post("/Update_GuideDetail", async (req, res, next) => {
    console.log("Update Guide Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            pdfUrlAr: req.body.data.pdfUrlAr,
            pdfUrlEn: req.body.data.pdfUrlAr,
            imageEn: req.body.data.imageEn,
            imageAr: req.body.data.imageAr,
            is_active: req.body.data.is_active,
        },
    ];
    await models.guides
        .update(
            {
                pdfUrlAr: values[0].pdfUrlAr,
                pdfUrlEn: values[0].pdfUrlAr,
                imageEn: values[0].imageEn,
                imageAr: values[0].imageAr,
                is_active: values[0].is_active,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Guide Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Guide Status
router.post("/Update_GuideStatus", async (req, res, next) => {
    console.log("Update Guide Status API calling", req.body.data);
    values = [
        {
            id: req.body.data.id,
            status: req.body.data.status,
        },
    ];
    await models.guides
        .update(
            {
                is_active: values[0].status,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const val = {
                id: values[0].id,
                is_active: values[0].status,
            };
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Guide Status Updated Successfully",
                    data: val,
                },
                accessTokenSecret
            );
            console.log("val", val);
            res.json({
                successful: true,
                message: "Successful",
                data: val,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

// Delete Single Guide
router.get("/Delete_SingleGuide/:id", async (req, res, next) => {
    const { id } = req.params;

    try {
        const guideData = await models.guides.findOne({
            where: {
                id: id,
            },
        });

        if (!guideData) {
            console.log("No Guide Found");
            return res.json({
                successful: false,
                message: "No Guide Found",
            });
        }

        const imageEnUrl = guideData.imageEn;
        const imageArUrl = guideData.imageAr;

        // Delete imageEn blob if imageEnUrl is available
        if (imageEnUrl) {
            const imageEnBlobName = decodeURIComponent(new URL(imageEnUrl).pathname.split('/').pop());
            const imageEnBlobClient = containerClient.getBlockBlobClient(imageEnBlobName);
            
            // Check if imageEn blob exists before attempting to delete
            const existsImageEn = await imageEnBlobClient.exists();
            if (existsImageEn && existsImageEn.exists) {
                await imageEnBlobClient.delete();
            }
        }

        // Delete imageAr blob if imageArUrl is available
        if (imageArUrl) {
            const imageArBlobName = decodeURIComponent(new URL(imageArUrl).pathname.split('/').pop());
            const imageArBlobClient = containerClient.getBlockBlobClient(imageArBlobName);
            
            // Check if imageAr blob exists before attempting to delete
            const existsImageAr = await imageArBlobClient.exists();
            if (existsImageAr && existsImageAr.exists) {
                await imageArBlobClient.delete();
            }
        }

        // Delete guide from database
        const deletedGuide = await models.guides.destroy({
            where: {
                id: id,
            },
        });

        if (deletedGuide) {
            console.log("Guide Deleted Successfully.");
            res.json({
                successful: true,
                message: "Guide Deleted Successfully.",
            });
        } else {
            console.log("Failed to delete Guide");
            res.json({
                successful: false,
                message: "Failed to delete Guide",
            });
        }
    } catch (error) {
        console.log("Failed To Delete Guide: ", error);
        res.status(500).json({
            successful: false,
            message: "Failed To Delete Guide: " + error,
        });
    }
});


//Update Guide En Pic
router.post("/Update_GuidepdfUrlEn", async (req, res, next) => {
    console.log("Update Guide En Pic API Calling", req.body.data);
   
    values = [
        {
            id: req.body.data.id,
            imageEn: req.body.data.imageEn,
        },
    ];
    await models.guides
        .update(
            {
                imageEn: values[0].imageEn,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Guide Pic Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Guide Ar Pic
router.post("/Update_GuidepdfUrlAr", async (req, res, next) => {
    console.log("Update Guide Ar Pic API Calling", req.body.data);
   
    values = [
        {
            id: req.body.data.id,
            imageAr: req.body.data.imageAr,
        },
    ];
    await models.guides
        .update(
            {
                imageAr: values[0].imageAr,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Sentence Pic Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

// Define storage for multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      // Specify the directory where the uploaded files should be stored
      cb(null, 'uploads/'); // You need to create this 'uploads' directory
    },
    filename: function (req, file, cb) {
      // Use the original name of the file for storing
      cb(null, file.originalname);
    }
  });
  
  // Initialize multer with the defined storage
const upload = multer({ storage: storage });

// Upload Guide En Pic
router.post("/GuidepdfUrlEn", async function (req, res) {
    upload(req, res, async function (err) {
      if (err instanceof multer.MulterError) {
        return res.json(err);
      } else if (err) {
        return res.json(err);
      }
  
      try {
        const file = req.file;
        console.log("Uploaded file:", file);
  
        if (!file) {
          console.log("No file provided");
          return res.status(400).json({ message: "No file provided" });
        }
  
        let blobName = file.originalname;
        console.log("Initial Blob name:", blobName);
        const contentType = file.mimetype;
        console.log('content type is: ', contentType);
        let blobClient = containerClient.getBlockBlobClient(blobName);
  
        let blobExists = await blobClient.exists();
        let suffix = 1;
  
        while (blobExists) {
          console.log("File with the same name already exists, adding suffix");
          blobName = `${file.originalname}_${suffix}`;
          blobClient = containerClient.getBlockBlobClient(blobName);
          blobExists = await blobClient.exists();
          suffix++;
        }
  
        const options = { blobHTTPHeaders: { blobContentType: file.mimetype } };
        const data = await blobClient.upload(file.buffer, file.size, options);
        console.log("File uploaded successfully:", data);
  
        const fileUrl = blobClient.url;
        console.log("File URL:", fileUrl);
  
        res.json({ 
          fileUrl,
          filename: blobName
        });
      } catch (error) {
        console.log("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file" });
      }
    });
  });
  
  // Upload Guide Ar Pic
  router.post("/GuidepdfUrlAr", async function (req, res) {
    upload(req, res, async function (err) {
      if (err instanceof multer.MulterError) {
        return res.json(err);
      } else if (err) {
        return res.json(err);
      }
  
      try {
        const file = req.file;
        console.log("Uploaded file:", file);
  
        if (!file) {
          console.log("No file provided");
          return res.status(400).json({ message: "No file provided" });
        }
  
        let blobName = file.originalname;
        console.log("Initial Blob name:", blobName);
        const contentType = file.mimetype;
        console.log('content type is: ', contentType);
        let blobClient = containerClient.getBlockBlobClient(blobName);
  
        let blobExists = await blobClient.exists();
        let suffix = 1;
  
        while (blobExists) {
          console.log("File with the same name already exists, adding suffix");
          blobName = `${file.originalname}_${suffix}`;
          blobClient = containerClient.getBlockBlobClient(blobName);
          blobExists = await blobClient.exists();
          suffix++;
        }
  
        const options = { blobHTTPHeaders: { blobContentType: file.mimetype } };
        const data = await blobClient.upload(file.buffer, file.size, options);
        console.log("File uploaded successfully:", data);
  
        const fileUrl = blobClient.url;
        console.log("File URL:", fileUrl);
  
        res.json({ 
          fileUrl,
          filename: blobName
        });
      } catch (error) {
        console.log("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file" });
      }
    });
  });
  

module.exports = router;
