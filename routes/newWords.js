const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");
const { containerClient } = require('../storage');

//Get Single New Word
router.get("/Get_SingleNewWord/:nw_id", (req, res, next) => {
    const { nw_id } = req.params;

    models.newWords
        .findAll({
            where: {
                id: nw_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("New Word Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Bew Word Get Successfully",
                });
            } else {
                console.log("No New Word Found");
                res.json({
                    successful: false,
                    message: "No New Word Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get newWord: ", err);
            res.json({
                successful: false,
                message: "Failed To Get newWord: " + err,
            });
        });
});

//Get All New Words
router.get("/Get_AllNewWords", (req, res, next) => {
    models.newWords
        .findAll({
            order: [["id", "DESC"]],
	        include: [
                { model: models.users, required: false },
              ]
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All New Words Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All New Words Successfully",
                });
            } else {
                console.log("No New Words Found");
                res.json({
                    successful: false,
                    message: "No newWords Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All New Words: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All New Words: " + err,
            });
        });
});

// Create New Word
router.post("/Create_NewWord", async (req, res, next) => {
    const { reference, isRead, word } = req.body.data;

    const values = {
        reference: reference,
        isRead: isRead,
        word: word,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the new word suggestion already exists
        const existingWord = await models.newWords.findOne({
            where: {
                word: word,
            },
        });

        if (existingWord) {
            console.log("New Word Suggestion already exists");
            return res.json({
                successful: false,
                message: "New Word Suggestion already exists",
            });
        }

        // Create a new word suggestion
        const newWord = await models.newWords.create(values);

        if (newWord) {
            console.log("New Word Suggested Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "New Word Suggested Successfully",
                    data: newWord,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "New Word Suggested Successfully",
                data: newWord,
            });
        }
    } catch (err) {
        console.log("Failed to Suggest New Word: ", err);
        return res.json({
            successful: false,
            message: "Failed to Suggest New Word: " + err,
        });
    }
});

//Update New Word Detail
router.post("/Update_NewWordDetail", async (req, res, next) => {
    console.log("Update New Word Detail API Calling:", req.body.data);
    console.log('Word is----', req.body.data.word);
    values = [
        {
            id: req.body.data.id,
            reference: req.body.data.reference,
            isRead: req.body.data.isRead,
            word: req.body.data.word,
        },
    ];
    await models.newWords
        .update(
            {
                reference: values[0].reference,
                isRead: values[0].isRead,
                word: values[0].word,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "newWord Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

router.post("/Update_NewWordStatus", async (req, res, next) => {
    console.log("Update New Word Status API Calling:", req.body.data);
  
    const { id, word, referenceisRead } = req.body.data;
  
    try {
      const [_, updatedNewWord] = await models.newWords.update(
        {
          isRead,
          updated_at: new Date().toISOString(),
        },
        {
          where: {
            id,
          },
          returning: true,
          plain: true,
          exclude: ["created_at", "updated_at"],
        }
      );
  
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "newWord Detail Updated Successfully",
          data: updatedNewWord.dataValues,
        },
        accessTokenSecret
      );
  
      console.log("Response Data: ", updatedNewWord.dataValues);
  
      res.json({
        successful: true,
        message: "Successful",
        data: updatedNewWord.dataValues,
        accessToken,
      });
    } catch (err) {
      console.log(err);
      res.json({
        message: "Failed" + err,
        successful: false,
      });
    }
  });
  
//Delete Single New Word
router.get("/Delete_SingleNewWord/:id", (req, res, next) => {
    const { id } = req.params;
  
    models.newWords
      .destroy({
        where: {
          id: id,
        },
      })
      .then((data) => {
        if (data?.length > 0) {
          console.log("newWord Deleted Successfully.");
          res.json({
            data: data,
            successful: true,
            message: "newWord Deleted Successfully.",
          });
        } else {
          console.log("No newWord Found");
          res.json({
            successful: false,
            message: "No newWord Found",
          });
        }
      })
      .catch(function (err) {
        console.log("Failed To Delete newWord: ", err);
        res.json({
          successful: false,
          message: "Failed To Delete newWord: " + err,
        });
      });
  });


module.exports = router;