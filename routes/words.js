const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");
const { containerClient } = require('../storage');
const multer = require('multer');
const path = require('path');

//Get Single Word
router.get("/Get_SingleWord/:wd_id", (req, res, next) => {
    const { wd_id } = req.params;

    models.words
        .findAll({
            where: {
                id: wd_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Word Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Word Get Successfully",
                });
            } else {
                console.log("No Word Found");
                res.json({
                    successful: false,
                    message: "No Word Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Word: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Word: " + err,
            });
        });
});

router.get("/Get_WordsByCategoryId/:cat_id", (req, res, next) => {
    const { cat_id } = req.params;
  
    // Convert the cat_id to string to ensure consistent comparison
    const categoryIdString = cat_id.toString();

    models.words
      .findAll()
      .then((data) => {
        const filteredWords = data.filter((word) => {
          // Split the categories string into an array of IDs
          const categoryIds = word.categories.split(',').map(id => id.trim());
          // Check if the categoryIds array contains the categoryIdString
          return categoryIds.includes(categoryIdString);
        });

        if (filteredWords.length !== 0) {
          console.log("Words Get Successfully");
          res.json({
            data: filteredWords,
            successful: true,
            message: "Words Get Successfully",
          });
        } else {
          console.log("No Words Found");
          res.json({
            successful: false,
            message: "No Words Found",
          });
        }
      })
      .catch((error) => {
        console.error("Error fetching words:", error);
        res.status(500).json({
          successful: false,
          message: "Internal Server Error",
        });
      });
  });

  

//Get All Words
router.get("/Get_AllWords", (req, res, next) => {
    models.words
        .findAll({
            order: [["english", "ASC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Words Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Words Successfully",
                });
            } else {
                console.log("No Words Found");
                res.json({
                    successful: false,
                    message: "No Words Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Words: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Words: " + err,
            });
        });
});

// Create Word
router.post("/Create_Word", async (req, res, next) => {
    const { 
        english,
        arabic,
        french,
        catAZEn,
        catAZAr,
        catAZFr,
        video,
        videoUrl,
        thumbnail,
        is_active,
        categories, 
    } = req.body.data;

    try {
        // Check if the word already exists
        const wordExists = await models.words.findOne({
            where: {
                english: english,
            },
        });

        if (wordExists) {
            console.log("Word already exists");
            return res.json({
                successful: false,
                message: "Word already exists",
            });
        }

        // Create a new word
        const newWord = await models.words.create({
            english: english,
            arabic: arabic,
            french: french,
            catAZEn: catAZEn,
            catAZAr: catAZAr,
            catAZFr: catAZFr,
            video: video,
            videoUrl: videoUrl,
            thumbnail: thumbnail,
            categories: categories,
            is_active: is_active,
            created_at: new Date().toISOString(),
        });

        console.log("Word Created Successfully");

        const accessToken = jwt.sign(
            {
                successful: true,
                message: "Word Created Successfully",
                data: newWord,
            },
            accessTokenSecret
        );

        return res.json({
            successful: true,
            message: "Word Created Successfully",
            data: newWord,
            accessToken: accessToken,
        });

    } catch (error) {
        console.log("Failed to Create New Word: ", error);
        
        return res.status(500).json({
            successful: false,
            message: "Failed to Create New Word: " + error,
        });
    }
});

//Update Word Detail
router.post("/Update_WordDetail", async (req, res, next) => {
    console.log("Update Word Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            english : req.body.data.english,
            arabic : req.body.data.arabic,
            french : req.body.data.french,
            catAZEn : req.body.data.catAZEn,
            catAZAr : req.body.data.catAZAr,
            catAZFr : req.body.data.catAZFr,
            video : req.body.data.video,
            videoUrl : req.body.data.videoUrl,
            thumbnail : req.body.data.thumbnail,
            is_active: req.body.data.is_active,
            categories: req.body.data.categories,
        },
    ];
    await models.words
        .update(
            {
                english : values[0].english,
                arabic : values[0].arabic,
                french : values[0].french,
                catAZEn : values[0].catAZEn,
                catAZAr : values[0].catAZAr,
                catAZFr : values[0].catAZFr,
                video : values[0].video,
                videoUrl : values[0].videoUrl,
                thumbnail : values[0].thumbnail,
                categories: values[0].categories,
                is_active: values[0].is_active,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Word Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Word Status
router.post("/Update_WordStatus", async (req, res, next) => {
    console.log("Update Word Status API calling", req.body.data);
    values = [
        {
            id: req.body.data.id,
            status: req.body.data.status,
        },
    ];
    await models.words
        .update(
            {
                is_active: values[0].status,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const val = {
                id: values[0].id,
                is_active: values[0].status,
            };
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Word Status Updated Successfully",
                    data: val,
                },
                accessTokenSecret
            );
            console.log("val", val);
            res.json({
                successful: true,
                message: "Successful",
                data: val,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

// Define storage for multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Specify the directory where the uploaded files should be stored
    cb(null, 'uploads/'); // You need to create this 'uploads' directory
  },
  filename: function (req, file, cb) {
    // Use the original name of the file for storing
    cb(null, file.originalname);
  }
});

// Initialize multer with the defined storage
const upload = multer({ storage: storage });

//Add Word Vid
router.post("/WordVid", upload.single('file'), async function (req, res) {
  try {
    console.log("Request received:", req);

    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }
    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // Set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Video URL:", imageUrl);

    res.json({ 
      imageUrl,
      filename: blobName
    });
  } catch (error) {
    console.log("Error uploading video:", error);
    res.status(500).json({ message: "Failed to upload video" });
  }
});

  //Update WordVid
  router.post("/Update_WordVid", async (req, res, next) => {
    console.log("Update Word Vid API Calling", req.body.data);
   
    const { id, videoUrl } = req.body.data; // Extract id and videoUrl from request body
  
    try {
      const updatedWord = await models.words.findOneAndUpdate({ id: id }, // Find the word by its id
        { 
            videoUrl: videoUrl, 
            updated_at: new Date().toISOString() 
        }, // Update the videoUrl and updated_at fields
        { 
            returning: true 
        } // Return the updated word document
      );
  
      if (updatedWord) {
        const accessToken = jwt.sign(
          {
            successful: true,
            message: "Word Video Updated Successfully",
            data: updatedWord,
          },
          accessTokenSecret
        );
        res.json({
          successful: true,
          message: "Successful",
          data: updatedWord,
          accessToken,
        });
      } else {
        res.status(404).json({
          message: "Word not found",
          successful: false,
        });
      }
    } catch (err) {
      console.log(err);
      res.status(500).json({
        message: "Failed to update word video: " + err,
        successful: false,
      });
    }
  });
  
 // Add Word Image
router.post("/WordImage", upload.single('file'), async function (req, res) {
  try {
    console.log("Request received:", req);

    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }
    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // Set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Image URL:", imageUrl);

    res.json({ 
      imageUrl,
      filename: blobName,
      
    });
  } catch (error) {
    console.log("Error uploading image:", error);
    res.status(500).json({ message: "Failed to upload image" });
  }
});

  
  //Update Word Image
  router.post("/Update_WordImage", async (req, res, next) => {
    console.log("Update Word Image API Calling", req.body.data);
   
    const { id, imageUrl } = req.body.data; // Extract id and imageUrl from request body
  
    try {
      const updatedWord = await models.words.findOneAndUpdate({ id: id }, // Find the word by its id
        {   
            thumbnail: imageUrl, 
            updated_at: new Date().toISOString() 
        }, // Update the thumbnail and updated_at fields
        { 
            returning: true 
        } // Return the updated word document
      );
  
      if (updatedWord) {
        const accessToken = jwt.sign(
          {
            successful: true,
            message: "Word Pic Updated Successfully",
            data: updatedWord,
          },
          accessTokenSecret
        );
        res.json({
          successful: true,
          message: "Successful",
          data: updatedWord,
          accessToken,
        });
      } else {
        res.status(404).json({
          message: "Word not found",
          successful: false,
        });
      }
    } catch (err) {
      console.log(err);
      res.status(500).json({
        message: "Failed to update word pic: " + err,
        successful: false,
      });
    }
  });
  
//Delete Single Word

router.get("/Delete_SingleWord/:id", async (req, res, next) => {
  const { id } = req.params;

  try {
      const wordData = await models.words.findOne({
          where: {
              id: id,
          },
      });

      if (!wordData) {
          console.log("No Word Found");
          return res.json({
              successful: false,
              message: "No Word Found",
          });
      }

      const videoUrl = wordData.video;
      const imageUrl = wordData.videoUrl;

      // Delete video blob if videoUrl is available
      if (videoUrl) {
          const videoBlobName = decodeURIComponent(new URL(videoUrl).pathname.split('/').pop());
          const videoBlobClient = containerClient.getBlockBlobClient(videoBlobName);
          
          // Check if video blob exists before attempting to delete
          const existsVideo = await videoBlobClient.exists();
          if (existsVideo && existsVideo.exists) {
              await videoBlobClient.delete();
          }
      }

      // Delete image blob if imageUrl is available
      if (imageUrl) {
          const imageBlobName = decodeURIComponent(new URL(imageUrl).pathname.split('/').pop());
          const imageBlobClient = containerClient.getBlockBlobClient(imageBlobName);
          
          // Check if image blob exists before attempting to delete
          const existsImage = await imageBlobClient.exists();
          if (existsImage && existsImage.exists) {
              await imageBlobClient.delete();
          }
      }

      // Delete word from database
      const deletedWord = await models.words.destroy({
          where: {
              id: id,
          },
      });

      if (deletedWord) {
          console.log("Word Deleted Successfully.");
          res.json({
              successful: true,
              message: "Word Deleted Successfully.",
          });
      } else {
          console.log("Failed to delete word");
          res.json({
              successful: false,
              message: "Failed to delete word",
          });
      }
  } catch (error) {
      console.log("Failed To Delete Word: ", error);
      res.status(500).json({
          successful: false,
          message: "Failed To Delete Word: " + error,
      });
  }
});




module.exports = router;