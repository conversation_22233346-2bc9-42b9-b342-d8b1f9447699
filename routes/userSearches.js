const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");

//Get Single User User Searches
router.get("/Get_SingleUserSearches/:user_id", (req, res, next) => {
    const { user_id } = req.params;

    models.userSearches.findAll({
  where: {
    user_id: user_id,
  },
  order: [["id", "DESC"]],
})


        .then((data) => {
            if (data?.length != 0) {
                console.log("User Searches Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "User Searches Get Successfully",
                });
            } else {
                console.log("No User Searches Found");
                res.json({
                    successful: false,
                    message: "No User Searches Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get User Searches: ", err);
            res.json({
                successful: false,
                message: "Failed To Get User Searches: " + err,
            });
        });
});

//Get All User Searches
router.get("/Get_AllUserSearches", (req, res, next) => {
    models.userSearches
        .findAll(
	    {
            include: [
                { model: models.users, required: false },
              ],
		order: [["created_at", "DESC"]],
        },
      )
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All User Searches Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All User Searches Successfully",
                });
            } else {
                console.log("No Favourites Found");
                res.json({
                    successful: false,
                    message: "No Favourites Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All User Searches: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All User Searches: " + err,
            });
        });
});

//Create User Searches
router.post("/Create_UserSearch", async (req, res, next) => {
    const { 
        user_id,
        param,
        video_url,
    } = req.body.data;

    values = [
        {
            user_id: req.body.data.user_id,
            param: req.body.data.param,
            video_url: req.body.data.video_url,
            created_at: new Date().toISOString(),
        },
    ];
    await models.userSearches
        .findAll({
 where: {
          user_id: user_id,
	      param: param, 
        },
        })
        .then((data) => {
            if (data?.length !== 0) {
                console.log("User Searches already exists");
                res.json({
                    successful: false,
                    message: "User Searches already exists",
                });
            } else {
                models.userSearches
                    .bulkCreate(values)
                    .then((x) => {
                        if (x?.length !== 0) {
                            const accessToken = jwt.sign(
                                {
                                    successful: true,
                                    message: "User Searches Created Successfully",
                                    data: x[0],
                                },
                                accessTokenSecret
                            );
                            res.json({
                                successful: true,
                                message: "User Searches Video added successfully",
                                data: x[0].id,
                            });
                        }
                    })
                    .catch(function (err) {
                        console.log("Failed to Create New User Searches: ", err);
                        res.json({
                            successful: false,
                            message: "Failed to Create New User Searches: " + err,
                        });
                    });
            }
        })
        .catch(function (err) {
            console.log("Request Data is Empty: ", err);
            res.json({
                successful: false,
                message: "Request Data is Empty: " + err,
            });
        });
});


//Delete Single User Searches
router.get("/Delete_SingleSearches/:user_id", (req, res, next) => {
    const { user_id } = req.params;
  
    models.userSearches
      .destroy({
        where: {
          user_id: user_id,
        },
      })
      .then((data) => {
        if (data?.length > 0) {
          console.log("User Searches Deleted Successfully.");
          res.json({
            data: data,
            successful: true,
            message: "User Searches Deleted Successfully.",
          });
        } else {
          console.log("No User Searches Found");
          res.json({
            successful: false,
            message: "No User Searches Found",
          });
        }
      })
      .catch(function (err) {
        console.log("Failed To Delete User Searches: ", err);
        res.json({
          successful: false,
          message: "Failed To Delete User Searches: " + err,
        });
      });
  });

  //Delete Single User Searches
router.get("/Delete_SingleSearch/:id", (req, res, next) => {
    const { id } = req.params;
  
    models.userSearches
      .destroy({
        where: {
          id: id,
        },
      })
      .then((data) => {
        if (data?.length > 0) {
          console.log("User Search Deleted Successfully.");
          res.json({
            data: data,
            successful: true,
            message: "User Search Deleted Successfully.",
          });
        } else {
          console.log("No User Searches Found");
          res.json({
            successful: false,
            message: "No User Searches Found",
          });
        }
      })
      .catch(function (err) {
        console.log("Failed To Delete User Search: ", err);
        res.json({
          successful: false,
          message: "Failed To Delete User Search: " + err,
        });
      });
  });


module.exports = router;