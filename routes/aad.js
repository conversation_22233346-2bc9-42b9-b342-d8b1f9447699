const express = require("express");
const router = express.Router();
const passport = require('../passport');  // Import the passport configuration


  
  router.post(
    "/auth/openid/return",
    passport.authenticate("azuread-openidconnect", { failureRedirect: "/" }),
    (req, res) => {
      if (req.isAuthenticated()) {
        // Authentication successful
        console.log("Azure AD B2C authentication successful");
        // Redirect to the dashboard or home page
        res.redirect("/dashboard");
      } else {
        // Authentication failed
        console.error("Azure AD B2C authentication failed");
        // Get the failure reason from query parameters
        const errorReason = req.query.error_description || "Unknown error";
        // Log the failure reason
        console.error("Failure reason:", errorReason);
        // Send a 401 Unauthorized response with an error message
        res.status(401).json({
          success: false,
          message: `Authentication failed: ${errorReason}`,
        });
      }
    }
  );
  
  
  // Route for Azure Active Directory authentication callback
  router.post(
    "/return",
    passport.authenticate("azuread-openidconnect", { failureRedirect: "/" }),
    (req, res) => {
      if (req.isAuthenticated()) {
        // Authentication successful
        console.log("Azure AD B2C authentication successful");
        // Redirect to the dashboard or home page
        res.redirect("/dashboard");
      } else {
        // Authentication failed
        console.error("Azure AD B2C authentication failed");
        // Get the failure reason from query parameters
        const errorReason = req.query.error_description || "Unknown error";
        // Log the failure reason
        console.error("Failure reason:", errorReason);
        // Send a 401 Unauthorized response with an error message
        res.status(401).json({
          success: false,
          message: `Authentication failed: ${errorReason}`,
        });
      }
    }
  );

module.exports = router;