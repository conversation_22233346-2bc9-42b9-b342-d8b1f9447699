const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
    accessTokenSecret,
} = require("../config");

//Get Single Category
router.get("/Get_SingleCategory/:cat_id", (req, res, next) => {
    const { cat_id } = req.params;

    models.categories
        .findAll({
            where: {
                id: cat_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Category Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Category Get Successfully",
                });
            } else {
                console.log("No Category Found");
                res.json({
                    successful: false,
                    message: "No Category Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Category: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Category: " + err,
            });
        });
});

//Get All Categories
router.get("/Get_AllCategories", (req, res, next) => {
    models.categories
        .findAll({
            order: [["categorieEn", "ASC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Categories Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Categories Successfully",
                });
            } else {
                console.log("No Categories Found");
                res.json({
                    successful: false,
                    message: "No Categories Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Categories: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Categories: " + err,
            });
        });
});

router.get("/Get_AllCategoriesMobile", (req, res, next) => {
    models.categories
        .findAll({
            order: [["categorieEn", "ASC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                // Create a new category object
                const allCategories = {
                    id: 0,
                    categorieEn: "All Categories",
                    categorieFr: "Toutes Catégories",
                    categorieAr: "جميع الفئات",
                    created_at: "", // Assign appropriate date if needed
                    updated_at: "" // Assign appropriate date if needed
                };

                // Insert the new category at the beginning of the data array
                data.unshift(allCategories);

                console.log("Get All Categories Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Categories Successfully",
                });
            } else {
                console.log("No Categories Found");
                res.json({
                    successful: false,
                    message: "No Categories Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Categories: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Categories: " + err,
            });
        });
});

// Create Category
router.post("/Create_Category", async (req, res, next) => {
  const { categorieAr, categorieEn, categorieFr } = req.body.data;

  const values = {
    categorieEn: categorieEn,
    categorieFr: categorieFr,
    categorieAr: categorieAr,
    created_at: new Date().toISOString(),
  };

  try {
    // Check if the category already exists
    const existingCategory = await models.categories.findOne({
      where: {
        categorieEn: categorieEn,
      },
    });

    if (existingCategory) {
      console.log("Category already exists");
      return res.json({
        successful: false,
        message: "Category already exists",
      });
    }

    // Create a new category
    const newCategory = await models.categories.create(values);

    if (newCategory) {
      console.log("Category Created Successfully");
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Category Created Successfully",
          data: newCategory,
        },
        accessTokenSecret
      );

      return res.json({
        successful: true,
        message: "Category Created Successfully",
        data: newCategory,
      });
    }
  } catch (err) {
    console.log("Failed to Create New Category: ", err);
    return res.json({
      successful: false,
      message: "Failed to Create New Category: " + err,
    });
  }
});


//Update Category Detail
router.post("/Update_CategoryDetail", async (req, res, next) => {
    console.log("Update Category Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            categorieEn: req.body.data.categorieEn,
            categorieFr: req.body.data.categorieFr,
            categorieAr: req.body.data.categorieAr,
        },
    ];
    await models.categories
        .update(
            {
                categorieEn: values[0].categorieEn,
                categorieFr: values[0].categorieFr,
                categorieAr: values[0].categorieAr,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Category Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update Category Status
router.post("/Update_CategoryStatus", async (req, res, next) => {
    console.log("Update Category Status API calling", req.body.data);
    values = [
        {
            id: req.body.data.id,
            status: req.body.data.status,
        },
    ];
    await models.categories
        .update(
            {
                is_active: values[0].status,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const val = {
                id: values[0].id,
                is_active: values[0].status,
            };
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Category Status Updated Successfully",
                    data: val,
                },
                accessTokenSecret
            );
            console.log("val", val);
            res.json({
                successful: true,
                message: "Successful",
                data: val,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

router.get("/Delete_SingleCategory/:id", async (req, res, next) => {
    const { id } = req.params;
  
    try {
        // Check if any word in the words table references the category
        const wordWithCategory = await models.words.findOne({
            where: {
                categories: {
                    [Op.like]: `%${id}%` // Check if the category ID exists in the comma-separated list
                }
            }
        });

        if (wordWithCategory) {
            // If a word references the category, don't delete it
            console.log("Cannot delete category as it is referenced by words.");
            return res.json({
                successful: false,
                message: "Cannot delete category as it is referenced by words.",
            });
        }

        // If no word references the category, proceed with deletion
        const deletedCount = await models.categories.destroy({
            where: {
                id: id,
            },
        });

        if (deletedCount > 0) {
            console.log("Category Deleted Successfully.");
            res.json({
                successful: true,
                message: "Category Deleted Successfully.",
            });
        } else {
            console.log("No Category Found");
            res.json({
                successful: false,
                message: "No Category Found",
            });
        }
    } catch (err) {
        console.log("Failed To Delete Category: ", err);
        res.json({
            successful: false,
            message: "Failed To Delete Category: " + err,
        });
    }
});

module.exports = router;