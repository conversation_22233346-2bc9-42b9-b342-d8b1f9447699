const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {accessTokenSecret} = require("../config");
const passport = require("../passport");

router.post(
    "/aad/auth/openid/return",
    passport.authenticate("azuread-openidconnect", { failureRedirect: "/" }),
    (req, res) => {
        // Successful login, create or update user in your database
        const { uid, email, name, lastLogin } = req.user;

        models.users
            .findOrCreate({
                where: { uid },
                defaults: {
                    email,
                    name,
                    lastLogin: new Date(),
                    // Add other fields as needed
                },
            })
            .then(([user, created]) => {
                // Update last login and other fields if necessary
                user.lastLogin = new Date();
                // ...

                user.save();

                const accessToken = jwt.sign(
                    {
                        successful: true,
                        message: "User Logged In Successfully",
                        data: {
                            id: user.id,
                            email: user.email,
                            name: user.name,
                            lastLogin: user.lastLogin,
                            // Include other fields as needed
                        },
                    },
                    accessTokenSecret
                );

                res.json({
                    successful: true,
                    message: "User Logged In Successfully",
                    data: {
                        id: user.id,
                        email: user.email,
                        name: user.name,
                        lastLogin: user.lastLogin,
                        // Include other fields as needed
                    },
                    accessToken,
                });
            })
            .catch((err) => {
                console.log("Error creating or updating user:", err);
                res.status(500).json({
                    successful: false,
                    message: "Failed to create or update user",
                    error: err,
                });
            });
    }
);


//Get Single User
router.get("/Get_SingleUser/:usrs_id", (req, res, next) => {
    const { usrs_id } = req.params;

    models.users
        .findAll({
            where: {
                id: usrs_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("User Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "User Get Successfully",
                });
            } else {
                console.log("No User Found");
                res.json({
                    successful: false,
                    message: "No User Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get User: ", err);
            res.json({
                successful: false,
                message: "Failed To Get User: " + err,
            });
        });
});

//Get All Users
router.get("/Get_AllUsers", (req, res, next) => {
    models.users
        .findAll({
            order: [["created_at", "DESC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Users Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Users Successfully",
                });
            } else {
                console.log("No Users Found");
                res.json({
                    successful: false,
                    message: "No Users Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Users: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Users: " + err,
            });
        });
});

//Admin Email Verify
router.post("/VerifyEmail", (req, res, next) => {
  values = [
    {
      Email: req.body.data.new_email,
    },
  ];

  models.admins
    .findAll({
      where: {
        Email: values[0].Email,
      },
    })
    .then((data) => {
      console.log(data?.length);

      if (data?.length != 0) {
        console.log("Email Already Exists");
        res.json({
          Successful: false,
          data: data,
        });
      } else {
        res.json({
          Successful: false,
        });
      }
    })
    .catch(function (err) {
      console.log("Error at find", err);
      res.json({
        Message: "Failed" + err,
        Successful: false,
      });
    });
});

//Create User
router.post("/Create_User", async (req, res, next) => {
    console.log("Request body:", req.body);
    const {
        uid,
        name,
        email, 
        lastLogin, 
        countryISOCode, 
        countryCode, 
        token, 
        emailVerified, 
        phone, 
    } = req.body.data;

    console.log("UID:", uid); // Log the UID for debugging

    const userExists = await models.users.findOne({
        where: {
            uid: uid,
            email: email,
        },
    });

    if (userExists) {
        console.log("User already exists");
// Update the lastLogin field for the existing user
        try {
            await models.users.update({ lastLogin: new Date().toISOString() }, {
                where: {
                    uid: uid,
                    email: email,
                },
            });

            console.log("Last login updated successfully");

        } catch (error) {
            console.log("Failed to update last login: ", error);
            res.status(500).json({
                successful: false,
                message: "Failed to update last login: " + error,
            });
            return; // Exit the function if update fails
        }
        res.json({
            successful: false,
            message: "User already exists",
            data: userExists // return existing user data
        });
    } else {
        try {
            const newUser = await models.users.create({
                lastLogin: lastLogin,
                countryISOCode: countryISOCode,
                token: token,
                emailVerified: emailVerified, 
                uid: uid,
                phone: phone,
                countryCode: countryCode,
                name: name,
                email: email,
                created_at: new Date().toISOString(),
            });
            console.log("User Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "User Created Successfully",
                    data: newUser,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "User Created Successfully",
                data: newUser,
                accessToken: accessToken
            });
        } catch (error) {
            console.log("Failed to Create New User: ", error);
            res.status(500).json({
                successful: false,
                message: "Failed to Create New User: " + error,
            });
        }
    }
});

//Update User Detail
router.post("/Update_UserDetail", async (req, res, next) => {
    console.log("Update User Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            lastLogin: req.body.data.lastLogin,
            countryISOCode: req.body.data.countryISOCode,
            token: req.body.data.token,
            emailVerified: req.body.data.emailVerified, 
            uid: req.body.data.uid,
            phone: req.body.data.phone,
            countryCode: req.body.data.countryCode,
            name: req.body.data.name,
            email: req.body.data.email,
        },
    ];
    await models.users
        .update(
            {
                lastLogin: values[0].lastLogin,
                countryISOCode: values[0].countryISOCode,
                token: values[0].token,
                emailVerified: values[0].emailVerified, 
                uid: values[0].uid,
                phone: values[0].phone,
                countryCode: values[0].countryCode,
                name: values[0].name,
                email: values[0].email,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "User Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Delete Single User
router.get("/Delete_SingleUser/:id", (req, res, next) => {
    const { id } = req.params;
  
    models.users
      .destroy({
        where: {
          id: id,
        },
      })
      .then((data) => {
        if (data?.length > 0) {
          console.log("User Deleted Successfully.");
          res.json({
            data: data,
            successful: true,
            message: "User Deleted Successfully.",
          });
        } else {
          console.log("No User Found");
          res.json({
            successful: false,
            message: "No User Found",
          });
        }
      })
      .catch(function (err) {
        console.log("Failed To Delete User: ", err);
        res.json({
          successful: false,
          message: "Failed To Delete User: " + err,
        });
      });
  });


module.exports = router;
