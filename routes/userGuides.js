const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const { accessTokenSecret } = require("../config");
const { containerClient } = require('../storage');
const multer = require("multer");
const path = require('path');

//Get Single UserGuide
router.get("/Get_SingleUserGuide/:ug_id", (req, res, next) => {
    const { ug_id } = req.params;

    models.userGuides
        .findAll({
            where: {
                id: ug_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("UserGuide Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "UserGuide Get Successfully",
                });
            } else {
                console.log("No UserGuide Found");
                res.json({
                    successful: false,
                    message: "No UserGuide Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get UserGuide: ", err);
            res.json({
                successful: false,
                message: "Failed To Get UserGuide: " + err,
            });
        });
});

//Get All UserGuides
router.get("/Get_AllUserGuides", (req, res, next) => {
    models.userGuides
        .findAll({
            order: [["created_at", "DESC"]],
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All UserGuides Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All UserGuides Successfully",
                });
            } else {
                console.log("No UserGuides Found");
                res.json({
                    successful: false,
                    message: "No UserGuides Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All UserGuides: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All UserGuides: " + err,
            });
        });
});

// Create UserGuide
router.post("/Create_UserGuide", async (req, res, next) => {
    const { language, title, picture, description, is_active } = req.body.data;

    const values = {
        language: language,
        title: title,
        picture: picture,
        description: description,
        is_active: is_active,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the UserGuide already exists
        const existingUserGuide = await models.userGuides.findOne({
            where: {
                title: title,
            },
        });

        if (existingUserGuide) {
            console.log("UserGuide already exists");
            return res.json({
                successful: false,
                message: "UserGuide already exists",
            });
        }

        // Create a new UserGuide
        const newUserGuide = await models.userGuides.create(values);

        if (newUserGuide) {
            console.log("UserGuide Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "UserGuide Created Successfully",
                    data: newUserGuide,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "UserGuide Created Successfully",
                data: newUserGuide,
            });
        }
    } catch (err) {
        console.log("Failed to Create New UserGuide: ", err);
        return res.json({
            successful: false,
            message: "Failed to Create New UserGuide: " + err,
        });
    }
});

//Update UserGuide Detail
router.post("/Update_UserGuideDetail", async (req, res, next) => {
    console.log("Update UserGuide Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            language: req.body.data.language,
            title: req.body.data.title,
            picture: req.body.data.picture,
            description: req.body.data.description,
            is_active: req.body.data.is_active,
        },
    ];
    await models.userGuides
        .update(
            {
                language: values[0].language,
                title: values[0].title,
                picture: values[0].picture,
                description: values[0].description,
                is_active: values[0].is_active,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "UserGuide Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update UserGuide Status
router.post("/Update_UserGuideStatus", async (req, res, next) => {
    console.log("Update UserGuide Status API calling", req.body.data);
    values = [
        {
            id: req.body.data.id,
            status: req.body.data.status,
        },
    ];
    await models.userGuides
        .update(
            {
                is_active: values[0].status,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const val = {
                id: values[0].id,
                is_active: values[0].status,
            };
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "UserGuide Status Updated Successfully",
                    data: val,
                },
                accessTokenSecret
            );
            console.log("val", val);
            res.json({
                successful: true,
                message: "Successful",
                data: val,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

//Update UserGuide Pic
router.post("/Update_UserGuidePic", async (req, res, next) => {
    console.log("Update UserGuide Pic API Calling", req.body.data);

    values = [
        {
            id: req.body.data.id,
            picture: req.body.data.picture,
        },
    ];
    await models.userGuides
        .update(
            {
                picture: values[0].picture,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "UserGuide Pic Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

// Define storage for multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // Specify the directory where the uploaded files should be stored
        cb(null, 'uploads/'); // You need to create this 'uploads' directory
    },
    filename: function (req, file, cb) {
        // Use the original name of the file for storing
        cb(null, file.originalname);
    }
});

// Initialize multer with the defined storage
var upload = multer({ storage: storage }).single("file");

router.post("/UserGuidePic", upload, async function (req, res) {
    try {
        const file = req.file;
        console.log("Uploaded file:", file);

        if (!file) {
            console.log("No file provided");
            return res.status(400).json({ message: "No file provided" });
        }
        const originalName = file.originalname;
        let blobName = file.originalname;
        console.log("Initial Blob name:", blobName);
        const contentType = file.mimetype;
        console.log('content type is: ', contentType);
        const filePath = file.path; // This is where you get the file path.
        console.log('filePath is: ', filePath);
        const fileExt = path.extname(originalName);
        console.log('fileExt is: ', fileExt);
        const fileNameWithoutExt = path.basename(originalName, fileExt);
        console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
        let blobClient = containerClient.getBlockBlobClient(blobName);

        let blobExists = await blobClient.exists();
        let suffix = 1;

        while (blobExists) {
            console.log("File with the same name already exists, adding suffix");
            blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
            blobClient = containerClient.getBlockBlobClient(blobName);
            blobExists = await blobClient.exists();
            suffix++;
        }

        const options = { blobHTTPHeaders: { blobContentType: file.type } };
        const data = await blobClient.upload(file.buffer, file.size, options);
        console.log("File uploaded successfully:", data);

        const fileUrl = blobClient.url;
        console.log("File URL:", fileUrl);

        res.json({
            fileUrl,
            filename: blobName
        });
    } catch (error) {
        console.log("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file" });
    }
});

//Delete Single UserGuide
router.get("/Delete_SingleUserGuide/:id", async (req, res, next) => {
    const { id } = req.params;

    try {
        const userGuideData = await models.userGuides.findOne({
            where: {
                id: id,
            },
        });

        if (!userGuideData) {
            console.log("No UserGuide Found");
            return res.json({
                successful: false,
                message: "No UserGuide Found",
            });
        }

        const imageUrl = userGuideData.picture;

        // Delete image blob if imageUrl is available
        if (imageUrl) {
            const imageBlobName = decodeURIComponent(new URL(imageUrl).pathname.split('/').pop());
            const imageBlobClient = containerClient.getBlockBlobClient(imageBlobName);

            // Check if image blob exists before attempting to delete
            const existsImage = await imageBlobClient.exists();
            if (existsImage && existsImage.exists) {
                await imageBlobClient.delete();
            }
        }

        // Delete user guide from database
        const deletedUserGuide = await models.userGuides.destroy({
            where: {
                id: id,
            },
        });

        if (deletedUserGuide) {
            console.log("UserGuide Deleted Successfully.");
            res.json({
                successful: true,
                message: "UserGuide Deleted Successfully.",
            });
        } else {
            console.log("Failed to delete UserGuide");
            res.json({
                successful: false,
                message: "Failed to delete UserGuide",
            });
        }
    } catch (error) {
        console.log("Failed To Delete UserGuide: ", error);
        res.status(500).json({
            successful: false,
            message: "Failed To Delete UserGuide: " + error,
        });
    }
});

module.exports = router;