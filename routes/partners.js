const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {accessTokenSecret} = require("../config");
const { containerClient } = require('../storage');
const multer = require('multer');
const path = require('path');

//Get Single Partner
router.get("/Get_SinglePartner/:wd_id", (req, res, next) => {
    const { wd_id } = req.params;

    models.partners
        .findAll({
            where: {
                id: wd_id,
            }
        })

        .then((data) => {
            if (data?.length != 0) {
                console.log("Partner Get Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Partner Get Successfully",
                });
            } else {
                console.log("No Partner Found");
                res.json({
                    successful: false,
                    message: "No Partner Found",
                });
            }
        })

        .catch(function (err) {
            console.log("Failed To Get Partner: ", err);
            res.json({
                successful: false,
                message: "Failed To Get Partner: " + err,
            });
        });
});

//Get All Partners
router.get("/Get_AllPartners", (req, res, next) => {
    models.partners
        .findAll({
           
        })
        .then((data) => {
            if (data?.length > 0) {
                console.log("Get All Partners Successfully");
                res.json({
                    data: data,
                    successful: true,
                    message: "Get All Partners Successfully",
                });
            } else {
                console.log("No Partners Found");
                res.json({
                    successful: false,
                    message: "No Partners Found",
                });
            }
        })
        .catch(function (err) {
            console.log("Failed To Get All Partners: ", err);
            res.json({
                successful: false,
                message: "Failed To Get All Partners: " + err,
            });
        });
});

// Create Partner
router.post("/Create_Partner", async (req, res, next) => {
    const { url, image } = req.body.data;

    const values = {
        image: image,
        url: url,
        created_at: new Date().toISOString(),
    };

    try {
        // Check if the partner already exists
        const existingPartner = await models.partners.findOne({
            where: {
                url: url,
            },
        });

        if (existingPartner) {
            console.log("Partner already exists");
            return res.json({
                successful: false,
                message: "Partner already exists",
            });
        }

        // Create a new partner
        const newPartner = await models.partners.create(values);

        if (newPartner) {
            console.log("Partner Created Successfully");
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Partner Created Successfully",
                    data: newPartner,
                },
                accessTokenSecret
            );

            return res.json({
                successful: true,
                message: "Partner Created Successfully",
                data: newPartner,
            });
        }
    } catch (err) {
        console.log("Failed to Create New Partner: ", err);
        return res.json({
            successful: false,
            message: "Failed to Create New Partner: " + err,
        });
    }
});


//Update Partner Detail
router.post("/Update_PartnerDetail", async (req, res, next) => {
    console.log("Update Partner Detail API Calling:", req.body.data);
    values = [
        {
            id: req.body.data.id,
            image : req.body.data.image,
            url: req.body.data.url,
            
        },
    ];
    await models.partners
        .update(
            {
                image : values[0].image,
                url : values[0].url,
                updated_at: new Date().toISOString(),
            },
            {
                where: {
                    id: values[0].id,
                },
                returning: true,
                plain: true,
                exclude: ["created_at", "updated_at"],
            }
        )
        .then((data) => {
            const accessToken = jwt.sign(
                {
                    successful: true,
                    message: "Partner Detail Updated Successfully",
                    data: data[1].dataValues,
                },
                accessTokenSecret
            );
            console.log("Response Data: ", data[1].dataValues);
            res.json({
                successful: true,
                message: "Successful",
                data: data[1].dataValues,
                accessToken,
            });
        })
        .catch(function (err) {
            console.log(err);
            res.json({
                message: "Failed" + err,
                successful: false,
            });
        });
});

// Define storage for multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Specify the directory where the uploaded files should be stored
    cb(null, 'uploads/'); // You need to create this 'uploads' directory
  },
  filename: function (req, file, cb) {
    // Use the original name of the file for storing
    cb(null, file.originalname);
  }
});

// Initialize multer with the defined storage
const upload = multer({ storage: storage });

// API route to handle file upload
router.post("/PartnerImage", upload.single('file'), async function (req, res) {
  try {
    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }

    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // Set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Image URL:", imageUrl);

    res.json({ 
      imageUrl,
      filename: blobName
    });
  } catch (error) {
    console.log("Error uploading image:", error);
    res.status(500).json({ message: "Failed to upload image" });
  }
});


// Update Partner Image API
router.post("/Update_PartnerImage", async (req, res, next) => {
  console.log("Update Partner Image API called", req.body.data);

  const { id, imageUrl } = req.body.data; // Extract id and imageUrl from request body

  try {
    const updatedPartner = await models.partners.findOneAndUpdate({ id: id },
      {   
          thumbnail: imageUrl, 
          updated_at: new Date().toISOString() 
      },
      { 
          returning: true 
      }
    );

    if (updatedPartner) {
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Partner Pic Updated Successfully",
          data: updatedPartner,
        },
        accessTokenSecret
      );
      res.json({
        successful: true,
        message: "Successful",
        data: updatedPartner,
        accessToken,
      });
    } else {
      res.status(404).json({
        message: "Partner not found",
        successful: false,
      });
    }
  } catch (err) {
    console.error("Error:", err);
    res.status(500).json({
      message: "Failed to update partner image: " + err,
      successful: false,
    });
  }
});
  
  //Update Partner Image
  router.post("/Update_PartnerImage", async (req, res, next) => {
    console.log("Update Partner Image API Calling", req.body.data);
   
    const { id, imageUrl } = req.body.data; // Extract id and imageUrl from request body
  
    try {
      const updatedPartner = await models.partners.findOneAndUpdate({ id: id }, // Find the word by its id
        {   
            thumbnail: imageUrl, 
            updated_at: new Date().toISOString() 
        }, // Update the thumbnail and updated_at fields
        { 
            returning: true 
        } // Return the updated word document
      );
  
      if (updatedPartner) {
        const accessToken = jwt.sign(
          {
            successful: true,
            message: "Partner Pic Updated Successfully",
            data: updatedPartner,
          },
          accessTokenSecret
        );
        res.json({
          successful: true,
          message: "Successful",
          data: updatedPartner,
          accessToken,
        });
      } else {
        res.status(404).json({
          message: "Partner not found",
          successful: false,
        });
      }
    } catch (err) {
      console.log(err);
      res.status(500).json({
        message: "Failed to update Sentence pic: " + err,
        successful: false,
      });
    }
  });


// Delete Single Partner
router.get("/Delete_SinglePartner/:id", async (req, res, next) => {
  const { id } = req.params;

  try {
      const partnerData = await models.partners.findOne({
          where: {
              id: id,
          },
      });

      if (!partnerData) {
          console.log("No Partner Found");
          return res.json({
              successful: false,
              message: "No Partner Found",
          });
      }

      const imageUrl = partnerData.image;

      // Delete image blob if imageUrl is available
      if (imageUrl) {
          const imageBlobName = decodeURIComponent(new URL(imageUrl).pathname.split('/').pop());
          const imageBlobClient = containerClient.getBlockBlobClient(imageBlobName);
          
          // Check if image blob exists before attempting to delete
          const existsImage = await imageBlobClient.exists();
          if (existsImage && existsImage.exists) {
              await imageBlobClient.delete();
          }
      }

      // Delete partner from database
      const deletedPartner = await models.partners.destroy({
          where: {
              id: id,
          },
      });

      if (deletedPartner) {
          console.log("Partner Deleted Successfully.");
          res.json({
              successful: true,
              message: "Partner Deleted Successfully.",
          });
      } else {
          console.log("Failed to delete Partner");
          res.json({
              successful: false,
              message: "Failed to delete Partner",
          });
      }
  } catch (error) {
      console.log("Failed To Delete Partner: ", error);
      res.status(500).json({
          successful: false,
          message: "Failed To Delete Partner: " + error,
      });
  }
});



module.exports = router;