const express = require("express");
const router = express.Router();
const models = require("../models/models");
const sequelize = require("sequelize");
const Op = sequelize.Op;
const jwt = require("jsonwebtoken");
const {
  accessTokenSecret,
} = require("../config");
const { containerClient } = require('../storage');
const multer = require('multer');
const path = require('path');

//Get Single Sentence
router.get("/Get_SingleSentence/:snt_id", (req, res, next) => {
  const { snt_id } = req.params;

  models.sentences
    .findAll({
      where: {
        id: snt_id,
      }
    })

    .then((data) => {
      if (data?.length != 0) {
        console.log("Sentence Get Successfully");
        res.json({
          data: data,
          successful: true,
          message: "Sentence Get Successfully",
        });
      } else {
        console.log("No Sentence Found");
        res.json({
          successful: false,
          message: "No Sentence Found",
        });
      }
    })

    .catch(function (err) {
      console.log("Failed To Get Sentence: ", err);
      res.json({
        successful: false,
        message: "Failed To Get Sentence: " + err,
      });
    });
});

//Get All Sentences
router.get("/Get_AllSentences", (req, res, next) => {
  models.sentences
    .findAll({
      order: [["english", "ASC"]],
    })
    .then((data) => {
      if (data?.length > 0) {
        console.log("Get All Sentences Successfully");
        res.json({
          data: data,
          successful: true,
          message: "Get All Sentences Successfully",
        });
      } else {
        console.log("No Sentences Found");
        res.json({
          successful: false,
          message: "No Sentences Found",
        });
      }
    })
    .catch(function (err) {
      console.log("Failed To Get All Sentences: ", err);
      res.json({
        successful: false,
        message: "Failed To Get All Sentences: " + err,
      });
    });
});

// Create Sentence
router.post("/Create_Sentence", async (req, res, next) => {
  const {
      english,
      arabic,
      french,
      video,
      videoUrl,
      thumbnail,
      is_active,
      idWord,
  } = req.body.data;

  const values = {
      english: english,
      arabic: arabic,
      french: french,
      video: video,
      videoUrl: videoUrl,
      thumbnail: thumbnail,
      is_active: is_active,
      idWord: idWord,
      created_at: new Date().toISOString(),
  };

  try {
      // Check if the sentence already exists
      const existingSentence = await models.sentences.findOne({
          where: {
              english: english,
          },
      });

      if (existingSentence) {
          console.log("Sentence already exists");
          return res.json({
              successful: false,
              message: "Sentence already exists",
          });
      }

      // Create a new sentence
      const newSentence = await models.sentences.create(values);

      if (newSentence) {
          console.log("Sentence Created Successfully");
          const accessToken = jwt.sign(
              {
                  successful: true,
                  message: "Sentence Created Successfully",
                  data: newSentence,
              },
              accessTokenSecret
          );

          return res.json({
              successful: true,
              message: "Sentence Created Successfully",
              data: newSentence,
          });
      }
  } catch (err) {
      console.log("Failed to Create New Sentence: ", err);
      return res.json({
          successful: false,
          message: "Failed to Create New Sentence: " + err,
      });
  }
});

//Update Sentence Detail
router.post("/Update_SentenceDetail", async (req, res, next) => {
  console.log("Update Sentence Detail API Calling:", req.body.data);
  values = [
    {
      id: req.body.data.id,
      english: req.body.data.english,
      arabic: req.body.data.arabic,
      french: req.body.data.french,
      video: req.body.data.video,
      videoUrl: req.body.data.videoUrl,
      thumbnail: req.body.data.thumbnail,
      is_active: req.body.data.is_active,
      idWord: req.body.data.idWord,
    },
  ];
  await models.sentences
    .update(
      {
        english: values[0].english,
        arabic: values[0].arabic,
        french: values[0].french,
        video: values[0].video,
        videoUrl: values[0].videoUrl,
        thumbnail: values[0].thumbnail,
        idWord: values[0].idWord,
        is_active: values[0].is_active,
        updated_at: new Date().toISOString(),
      },
      {
        where: {
          id: values[0].id,
        },
        returning: true,
        plain: true,
        exclude: ["created_at", "updated_at"],
      }
    )
    .then((data) => {
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Sentence Detail Updated Successfully",
          data: data[1].dataValues,
        },
        accessTokenSecret
      );
      console.log("Response Data: ", data[1].dataValues);
      res.json({
        successful: true,
        message: "Successful",
        data: data[1].dataValues,
        accessToken,
      });
    })
    .catch(function (err) {
      console.log(err);
      res.json({
        message: "Failed" + err,
        successful: false,
      });
    });
});

//Update Sentence Status
router.post("/Update_SentenceStatus", async (req, res, next) => {
  console.log("Update Sentence Status API calling", req.body.data);
  values = [
    {
      id: req.body.data.id,
      status: req.body.data.status,
    },
  ];
  await models.sentences
    .update(
      {
        is_active: values[0].status,
        updated_at: new Date().toISOString(),
      },
      {
        where: {
          id: values[0].id,
        },
        returning: true,
        exclude: ["created_at", "updated_at"],
      }
    )
    .then((data) => {
      const val = {
        id: values[0].id,
        is_active: values[0].status,
      };
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Sentence Status Updated Successfully",
          data: val,
        },
        accessTokenSecret
      );
      console.log("val", val);
      res.json({
        successful: true,
        message: "Successful",
        data: val,
        accessToken,
      });
    })
    .catch(function (err) {
      console.log(err);
      res.json({
        message: "Failed" + err,
        successful: false,
      });
    });
});

// Define storage for multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Specify the directory where the uploaded files should be stored
    cb(null, 'uploads/'); // You need to create this 'uploads' directory
  },
  filename: function (req, file, cb) {
    // Use the original name of the file for storing
    cb(null, file.originalname);
  }
});

// Initialize multer with the defined storage
const upload = multer({ storage: storage });

//Add Word Vid
router.post("/SentenceVid", upload.single('file'), async function (req, res) {
  try {
    console.log("Request received:", req);

    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }

    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Video URL:", imageUrl);

    res.json({
      imageUrl,
      filename: blobName
    });
  } catch (error) {
    console.log("Error uploading video:", error);
    res.status(500).json({ message: "Failed to upload video" });
  }
});
//Update SentenceVid
router.post("/Update_SentenceVid", async (req, res, next) => {
  console.log("Update Word Vid API Calling", req.body.data);

  const { id, videoUrl } = req.body.data; // Extract id and videoUrl from request body

  try {
    const updatedSentence = await models.sentences.findOneAndUpdate({ id: id }, // Find the word by its id
      {
        videoUrl: videoUrl,
        updated_at: new Date().toISOString()
      }, // Update the videoUrl and updated_at fields
      {
        returning: true
      } // Return the updated word document
    );

    if (updatedSentence) {
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Sentence Video Updated Successfully",
          data: updatedSentence,
        },
        accessTokenSecret
      );
      res.json({
        successful: true,
        message: "Successful",
        data: updatedSentence,
        accessToken,
      });
    } else {
      res.status(404).json({
        message: "Sentence not found",
        successful: false,
      });
    }
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Failed to update Sentence video: " + err,
      successful: false,
    });
  }
});

//Add Sentence Image
router.post("/SentenceImage", upload.single('file'), async function (req, res) {
  try {
    console.log("Request received:", req);

    // Extract the file from req.file
    const file = req.file;
    console.log("Uploaded file:", file);

    if (!file) {
      console.log("No file provided");
      return res.status(400).json({ message: "No file provided" });
    }

    const originalName = file.originalname;
    let blobName = file.originalname;
    console.log("Initial Blob name:", blobName);
    const contentType = file.mimetype;
    console.log('content type is: ', contentType);
    const filePath = file.path; // This is where you get the file path.
    console.log('filePath is: ', filePath);
    const fileExt = path.extname(originalName);
    console.log('fileExt is: ', fileExt);
    const fileNameWithoutExt = path.basename(originalName, fileExt);
    console.log('fileNameWithoutExt is: ', fileNameWithoutExt);
    let blobClient = containerClient.getBlockBlobClient(blobName);

    // Check if the blob already exists
    let blobExists = await blobClient.exists();
    let suffix = 1;

    while (blobExists) {
      console.log("File with the same name already exists, adding suffix");
      blobName = `${fileNameWithoutExt}_${suffix}${fileExt}`;
      console.log("new Blob name:", blobName);
      blobClient = containerClient.getBlockBlobClient(blobName);
      blobExists = await blobClient.exists();
      suffix++;
    }

    // set mimetype as determined from browser with file upload control
    const options = { blobHTTPHeaders: { blobContentType: file.type } };
    // Upload the file
    const data = await blobClient.uploadFile(filePath, options);
    console.log("File uploaded successfully:", data);

    const imageUrl = blobClient.url;
    console.log("Video URL:", imageUrl);

    res.json({
      imageUrl,
      filename: blobName
    });
  } catch (error) {
    console.log("Error uploading video:", error);
    res.status(500).json({ message: "Failed to upload video" });
  }
});

//Update Sentence Image
router.post("/Update_SentenceImage", async (req, res, next) => {
  console.log("Update Sentence Image API Calling", req.body.data);

  const { id, imageUrl } = req.body.data; // Extract id and imageUrl from request body

  try {
    const updatedSentence = await models.sentences.findOneAndUpdate({ id: id }, // Find the word by its id
      {
        thumbnail: imageUrl,
        updated_at: new Date().toISOString()
      }, // Update the thumbnail and updated_at fields
      {
        returning: true
      } // Return the updated word document
    );

    if (updatedSentence) {
      const accessToken = jwt.sign(
        {
          successful: true,
          message: "Sentence Pic Updated Successfully",
          data: updatedSentence,
        },
        accessTokenSecret
      );
      res.json({
        successful: true,
        message: "Successful",
        data: updatedSentence,
        accessToken,
      });
    } else {
      res.status(404).json({
        message: "Sentence not found",
        successful: false,
      });
    }
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Failed to update Sentence pic: " + err,
      successful: false,
    });
  }
});

//Delete Single Sentence
router.get("/Delete_SingleSentence/:id", async (req, res, next) => {
  const { id } = req.params;

  try {
    const sentenceData = await models.sentences.findOne({
      where: {
        id: id,
      },
    });

    if (!sentenceData) {
      console.log("No Sentence Found");
      return res.json({
        successful: false,
        message: "No Sentence Found",
      });
    }

    const videoUrl = sentenceData.videoUrl;
    const imageUrl = sentenceData.thumbnail;

    // Delete video blob if videoUrl is available
    if (videoUrl) {
      const videoBlobName = decodeURIComponent(new URL(videoUrl).pathname.split('/').pop());
      const videoBlobClient = containerClient.getBlockBlobClient(videoBlobName);
      
      // Check if video blob exists before attempting to delete
      const existsVideo = await videoBlobClient.exists();
      if (existsVideo && existsVideo.exists) {
        await videoBlobClient.delete();
      }
    }

    // Delete image blob if imageUrl is available
    if (imageUrl) {
      const imageBlobName = decodeURIComponent(new URL(imageUrl).pathname.split('/').pop());
      const imageBlobClient = containerClient.getBlockBlobClient(imageBlobName);
      
      // Check if image blob exists before attempting to delete
      const existsImage = await imageBlobClient.exists();
      if (existsImage && existsImage.exists) {
        await imageBlobClient.delete();
      }
    }

    // Delete sentence from database
    const deletedSentence = await models.sentences.destroy({
      where: {
        id: id,
      },
    });

    if (deletedSentence) {
      console.log("Sentence Deleted Successfully.");
      res.json({
        successful: true,
        message: "Sentence Deleted Successfully.",
      });
    } else {
      console.log("Failed to delete sentence");
      res.json({
        successful: false,
        message: "Failed to delete sentence",
      });
    }
  } catch (error) {
    console.log("Failed To Delete Sentence: ", error);
    res.status(500).json({
      successful: false,
      message: "Failed To Delete Sentence: " + error,
    });
  }
});



module.exports = router;